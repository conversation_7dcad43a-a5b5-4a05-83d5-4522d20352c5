use actix_cors::Cors;
use actix_web::cookie::time::Duration;
use actix_session::config::PersistentSession;
use actix_web::web::Data;
use actix_web::HttpResponseBuilder;
use console::style;
use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};
use localsend::server::UDPService;
use log::kv::Value;
use rand::Rng;
use sea_orm::Database;
use actix_web::{
    get, middleware, web, App, Error, HttpRequest, HttpResponse, HttpServer, Result, cookie::Key
};

use fs_extra::file::{copy as copy_file, CopyOptions as CopyFileOption};
use fs_extra::dir::{copy as copy_dir, CopyOptions as CopyDirOption};
use actix_session::{SessionMiddleware, storage::CookieSessionStore};
use service::get_path_in_exe_dir;
use tokio::spawn;

use tokio::{runtime, sync::mpsc};
use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};
use tracing::{debug, info};
use dialoguer::{theme::ColorfulTheme, MultiSelect};

use std::io::Write;
use std::net::Ipv4Addr;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use std::time::Instant;
use std::collections::HashMap;

use log4rs::append::console::ConsoleAppender;
use log4rs::append::file::FileAppender;
use log4rs::encode::pattern::PatternEncoder;
use log4rs::config::{Appender, Config, Root};


use actix_web_actors::ws;
use urlencoding;

use listenfd::ListenFd;
use migration::{Migrator, MigratorTrait};
use std::{env, thread};
use websocket::server::{ClientMessage, WSServer};
use actix::prelude::*;
use context::{AppState, BusinessError, BusinessResponse, HttpFileCache};
use controller_admin::{
    get_section_teacher_login_config,
    post_course_teacher_remote_login,
    get_remote_all_teams_by_school_year,
    get_remote_team_users_by_userid,
    post_remote_team_users_sync,
    get_remote_all_trains_by_series,
    get_remote_all_series,
    // post_remote_train_sync,
    // post_remote_train_series_sync,
    // post_remote_train_plan_report,
    post_remote_check_team_users_sync,
    section_teacher_login,
    get_local_teams_by_school_year,
    // get_local_trains_by_school_year,
    get_local_users_by_team_id,
    // post_local_trainplan,
    // put_local_trainplan_start,
    // put_local_trainplan_end,
    // put_local_trainplan_reset,
    // put_local_trainplan_restore,
    // pub_local_trainplan_pause,
    // delete_trainplans_by_ids,
    // clear_student_records,

    // get_local_train_series,

    // get_local_terminal,
    // delete_local_train,
    delete_local_team, 
    post_train_remote_update_check,
    post_train_remote_update_download, post_remote_train_sync_mp4,
    put_user_password_by_ids,
    // put_local_trainplan_update,
    // put_train_user_record_admin,

    get_terminals,
    // reset_terminal_by_ips,
    delete_terminal_by_ip,
    clear_terminals,
    transfer_terminal,
    put_terminal_ip_bind_config,
    enable_no_password_login,
    enable_modern_style,
    enable_correction_mode,
    get_section_result,
    get_chapter_result,
    
    upload_train_file,
    upload_school_config,
    upload_class_and_students,
    upload_users,
    // upload_train,
    // upload_train_series,
    course_download,
    get_download_progress,

    send_student_message,
    // get_student_answer_admin,
    get_value_by_key,
    put_value_by_key,
    get_online_count,
    get_all_teams,
    export_course_progress_excel,
    get_code_record,
    start_class,
    get_local_terminal,
    get_section_record
};

use controller_web::{
    post_section_student_prelogin,
    post_section_student_login,
    get_train_student_session,
    // post_train_plan_registry,
    // put_train_user_record,
    // post_train_user_submit,
    get_server_ok,
    student_unbind_terminal,
    post_section_student_reset_password,
    // post_train_student_correction_record_create,
    // post_train_student_correction_record_update,
    // post_download_count,
    // get_download_mp4,
    upload_operation_file,
    // get_trainplan_list_all,
    // get_train_student_plan_record,
    // get_plan_train_info,
    get_config    
};
use course::{
    get_course_indics,
    get_course_directory,
    commit_oi_record,
    commit_source_block_record,
    get_commit_record,
    get_course_all,
    create_course_sections,
    update_course_chapter_rule,
    commit_ai_record,
    get_course_files,
    post_local_course,
    put_local_course_reset,
    get_course_by_config,
    get_file_content,
    commit_scratch_record,
    commit_oj_record,
    commit_microapp_record,
    commit_code_anwser_record,
    commit_operation_record,
    commit_ppt_record,
    query_section_content_and_type,
    get_micro_app_primary_content_by_uuid,
    code_run,
    get_user_session,
    get_statis_result,
    batch_delete_course,
    get_course_slug_list,
    course_files_reset,
    save_scratch_record,
    clear_student_record,
    test,
    test_websocket
};
pub use file_operate::{
    check_ipython,
    init_ipython,
    run_ipython,
    stop_ipython,
    prepare_fs,
    check_ipython_status,
    open_fs,
    close_virtual_disk,
    get_file_content_by_path,
    post_course_download_course_zip
};
use crate::course::update_course;

pub mod context;
pub mod controller_web;
pub mod controller_admin;
pub mod file_operate;
pub mod course;
pub mod faye;

// 定义常量
const ALIAS: &str = "teacher_server"; // 设备别名
const INTERFACE_ADDR: Ipv4Addr = Ipv4Addr::new(0, 0, 0, 0); // 接口地址
const MULTICAST_ADDR: Ipv4Addr = Ipv4Addr::new(224, 0, 0, 168); // 组播地址
const MULTICAST_PORT: u16 = 53317; // 组播端口
const MESSAGE_MULTICAST_ADDR: Ipv4Addr = Ipv4Addr::new(224, 0, 0, 169); // 组播地址
const MESSAGE_MULTICAST_PORT: u16 = 53318; // 组播端口

// Websocket入口点
#[get("/ws/{room}")]
pub async fn websocket_route(
    req: HttpRequest,
    room: web::Path<String>,
    stream: web::Payload,
    srv: web::Data<Addr<websocket::server::WSServer>>
) -> Result<HttpResponse, Error> {
    let room = room.into_inner();
    let connection_info = req.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();

    ws::start(
        websocket::session::WsChatSession {
            id: 0,
            hb: Instant::now(),
            room: room.to_owned(),
            client_ip: client_ip.to_owned(),
            addr: srv.get_ref().clone()
        },
        &req,
        stream,
    )
}

async fn not_found(request: HttpRequest) -> Result<HttpResponse, Error> {
    let mut ctx = tera::Context::new();
    ctx.insert("uri", request.uri().path());

    // 写入日志
    let connection = request.connection_info();
    let remote_addr = connection.peer_addr().unwrap_or("unknown");
    log::warn!("非法访问 {}, 来自IP {}", request.uri().path(), remote_addr);

    // 返回404
    Ok(HttpResponse::NotFound().content_type("text/html").body("非法访问，您的IP已被记录！"))
}

// /microapps和/file/course开头的请求，都由这个函数处理
#[get("/file/course/{filename:.*}")]
async fn static_files(
    req: HttpRequest,
    file_cache_map: web::Data<Mutex<HashMap<String, HttpFileCache>>>,
) -> Result<HttpResponse, Error> {
    // 获取请求文件名
    let filename: &str = req.match_info().query("filename");
    // 对文件名进行解码
    let filename = urlencoding::decode(filename).unwrap();
    // 将解码后的文件名转为&str类型
    let filename = filename.as_ref();
    // 拼接文件路径
    let file_path: PathBuf = get_path_in_exe_dir("course").join(filename);

    // 获取文件大小和最后更新时间
    let metadata = std::fs::metadata(&file_path);
    if metadata.is_err() {
        return Ok(HttpResponse::NotFound().content_type("text/html").body("文件不存在"));
    }

    let metadata = metadata.unwrap();
    let modified = metadata.modified().unwrap();

    // 检查文件是否在缓存中，如果在缓存中则直接返回
    let mut file_cache_map = file_cache_map.lock().unwrap();

    if file_cache_map.contains_key(filename) {
        // println!("文件 {} 在缓存中存在", filename);
        let file_cached_item = file_cache_map.get(filename).unwrap();
        let cached_modified = file_cached_item.modified;

        // 磁盘上的文件没有发生变化，直接返回缓存
        if cached_modified == modified {
            // println!("文件 {} 在缓存中，且未发生变化，命中", filename);

            // 返回文件内容
            return Ok(HttpResponse::Ok()
                .content_type(file_cached_item.mime.clone())
                .append_header(("Access-Control-Allow-Origin","*/*"))
                .body(file_cached_item.content.clone()));
        }
    }

    // println!("文件 {} 未在缓存中，尝试读取", filename);
    // println!("缓存中文件数量 {}", file_cache_map.len());

    // 判定文件的MIME类型
    let mime_type = mime_guess::from_path(&file_path).first_or_octet_stream().to_string();

    // 读取文件内容
    let content = std::fs::read(&file_path);
    if content.is_err() {
        return Ok(HttpResponse::NotFound().content_type("text/html").body("文件不存在"));
    }

    let content = content.unwrap();

    // 建立缓存
    let file_cache = HttpFileCache {
        mime: mime_type.clone(),
        content: content.clone(),
        modified: modified
    };

    // 获取可以写入的缓存字典
    file_cache_map.insert(filename.to_string(), file_cache);

    // 返回文件内容
    Ok(HttpResponse::Ok()
        .content_type(mime_type)
        .append_header(("Last-Modified", metadata.modified().unwrap().elapsed().unwrap().as_secs().to_string()))
        .append_header(("Access-Control-Allow-Origin","*/*"))
        .body(content))
}

// /microapps和/file/course开头的请求，都由这个函数处理
#[get("/microapps/{filename:.*}")]
async fn static_files1(
    req: HttpRequest,
    file_cache_map: web::Data<Mutex<HashMap<String, HttpFileCache>>>,
) -> Result<HttpResponse, Error> {
    // 获取请求文件名
    let filename = req.match_info().query("filename");
    // 对文件名进行解码
    let filename = urlencoding::decode(filename).unwrap();
    // 将解码后的文件名转为&str类型
    let filename = filename.as_ref();
    // 拼接文件路径
    let mut file_path: PathBuf = get_path_in_exe_dir("../microapps").join(filename);    // 结尾为/get时映射路径
    // println!("文件路径修改前 {}", file_path.to_str().unwrap());

    if filename.ends_with("/get/") {
        // println!("该文件路径需要修改 {}", file_path.to_str().unwrap());
        let filename = filename.strip_suffix("/get/").unwrap();
        if filename.contains("internalapi/asset") {
            let filename = filename.replace("internalapi/asset", "library-files");
            // 拼接文件路径
            file_path = get_path_in_exe_dir("../microapps").join(filename);
        }
    } 
    // println!("文件路径修改后 {}", file_path.to_str().unwrap());
    // 获取文件大小和最后更新时间
    let metadata = std::fs::metadata(&file_path);
    if metadata.is_err() {
        return Ok(HttpResponse::NotFound().content_type("text/html").body("文件不存在"));
    }

    let metadata = metadata.unwrap();
    let modified = metadata.modified().unwrap();

    // 检查文件是否在缓存中，如果在缓存中则直接返回
    let mut file_cache_map = file_cache_map.lock().unwrap();

    if file_cache_map.contains_key(filename) {
        // println!("文件 {} 在缓存中存在", filename);
        let file_cached_item = file_cache_map.get(filename).unwrap();
        let cached_modified = file_cached_item.modified;

        // 磁盘上的文件没有发生变化，直接返回缓存
        if cached_modified == modified {
            // println!("文件 {} 在缓存中，且未发生变化，命中", filename);

            // 返回文件内容
            return Ok(HttpResponse::Ok()
                .content_type(file_cached_item.mime.clone())
                .append_header(("Access-Control-Allow-Origin","*/*"))
                .body(file_cached_item.content.clone()));
        }
    }

    // println!("文件 {} 未在缓存中，尝试读取", filename);
    // println!("缓存中文件数量 {}", file_cache_map.len());

    // 判定文件的MIME类型
    let mime_type = mime_guess::from_path(&file_path).first_or_octet_stream().to_string();

    // 读取文件内容
    let content = std::fs::read(&file_path);
    if content.is_err() {
        return Ok(HttpResponse::NotFound().content_type("text/html").body("文件不存在"));
    }

    let content = content.unwrap();

    // 建立缓存
    let file_cache = HttpFileCache {
        mime: mime_type.clone(),
        content: content.clone(),
        modified: modified
    };

    // 获取可以写入的缓存字典
    file_cache_map.insert(filename.to_string(), file_cache);

    let mut response_builder: HttpResponseBuilder = HttpResponse::Ok()
    .content_type(mime_type)
    .append_header(("Access-Control-Allow-Origin","*/*"))
    .take();

    // 如果能够读取文件时间，增加Last-Modified头
    if let Ok(time) = modified.elapsed() {
        response_builder.append_header(("Last-Modified", time.as_secs().to_string()));
    }

    // 返回文件内容
    Ok(response_builder.body(content))
}



// 生成一个64字节的随机Key
fn generate_key() -> Vec<u8> {
    let mut key_bytes = vec![0u8; 64]; // 初始化一个长度为 64 的字节向量
    let mut rng = rand::thread_rng();

    for i in 0..64 {
        loop {
            // 生成随机字节
            key_bytes[i] = rng.gen(); // 生成一个随机字节

            // 检查字节是否为字母或数字
            if (b'0' <= key_bytes[i] && key_bytes[i] <= b'9') ||
               (b'a' <= key_bytes[i] && key_bytes[i] <= b'z') ||
               (b'A' <= key_bytes[i] && key_bytes[i] <= b'Z')
            {
                break; // 如果字节符合条件，跳出循环
            }
        }
    }

    // 将字节向量转换为十六进制字符串并返回
    key_bytes
}
fn on_client_message(msg:&ClientMessage){
    println!("收到消息{:?}", msg.msg)
}


#[actix_web::main]
async fn start(port:u16, cookie_key: Option<String>) -> std::io::Result<()> {
    // let websocket_server = WSServer::new(Box::new(on_client_message)).start();
    // --- 日志 ---
    // 配置环境变量APP_LOG=1才有日志
    let enable_logger = env::var("APP_LOG");
    if enable_logger.is_ok() && enable_logger.unwrap() == "1" {
        let stdout = ConsoleAppender::builder().build();
        let stderr = ConsoleAppender::builder().build();

         // 创建一个文件 appender
        let logfile = FileAppender::builder()
            .encoder(Box::new(PatternEncoder::new("{d} {l} - {m}\n")))
            .build("server.log")
            .unwrap();
    
        // 创建一个日志配置
        let config = Config::builder()
            .appender(Appender::builder().build("stdout", Box::new(stdout)))
            .appender(Appender::builder().build("stderr", Box::new(stderr)))
            .appender(Appender::builder().build("logfile", Box::new(logfile)))
            // .logger(Logger::builder().build("app::backend::db", LevelFilter::Info))
            .build(Root::builder().appender("logfile").build(log::LevelFilter::Info))
            .unwrap();
        
        // 初始化日志系统
        let _handle = log4rs::init_config(config).unwrap();
    }

    // --- 目录 ---
    let tmp_path = get_path_in_exe_dir("tmp");

    // 初始化配套目录
    match std::fs::create_dir_all(tmp_path) {
        Ok(_r) => (),
        Err(e) => {
            panic!("递归创建目标目录失败 {}", e.to_string());
        }
    };

    let static_update_dir = get_path_in_exe_dir("static").join("update");
    match std::fs::create_dir_all(static_update_dir) {
        Ok(_r) => (),
        Err(e) => {
            panic!("递归创建目标目录失败 {}", e.to_string());
        }
    };

    // 没有数据库文件的时候执行初始化
    let db_path = get_path_in_exe_dir("hxr.db");
    if !db_path.exists() {
        // 拷贝初始化用数据库与训练文件到外部
        let options = CopyFileOption::new(); 

        let init_hxr_db = get_path_in_exe_dir("init").join("hxr.db");
        match copy_file(init_hxr_db, &db_path, &options) {
            Ok(_r) => (),
            Err(e) => {
                panic!("拷贝初始化数据库失败 {}", e.to_string())
            }
        };

        // let options = CopyDirOption::new(); 
        // let init_static_train = get_path_in_exe_dir("init").join("static").join("train");
        // let static_dir = get_path_in_exe_dir("static");
        // match copy_dir(init_static_train, static_dir, &options) {
        //     Ok(_r) => (),
        //     Err(e) => {
        //         panic!("拷贝初始化目录失败 {}", e.to_string())
        //     }
        // };
    }

    // --- 数据库 ---
    // 读取环境变量，拼接主机、端口
    let db_url = format!("sqlite://{}?mode=rwc", db_path.to_str().unwrap());

    // 服务器监听地址
    let server_url = format!("0.0.0.0:{}", port);

    // establish connection to database and apply migrations
    // -> create post table if not exists
    // SqlxSqliteConnector::from_sqlx_sqlite_pool(pool)
    let conn: sea_orm::prelude::DatabaseConnection = Database::connect(&db_url).await.unwrap();
    let conn_clone = conn.clone();
    let migrate_result = Migrator::up(&conn, None).await;
    if migrate_result.is_err() {
        println!("数据库迁移失败，可能会导致接口工作异常，升级过数据库又降低了服务器版本吗？ {:?}", migrate_result.err());
    }
    
    fn on_client_message(msg:&ClientMessage, server: Arc<WSServer>, conn: sea_orm::prelude::DatabaseConnection, download_progress: Arc<Mutex<HashMap<String, context::ProgressData>>> ){
        match msg.room.as_str() {
            "download" => {
                let download_info:serde_json::Value = serde_json::from_str(msg.msg.as_str()).unwrap();
                let download_type = download_info["type"].as_str().unwrap();
                let course_slug = download_info["courseSlug"].as_str().unwrap();
                let url = download_info["url"].as_str().unwrap();

                let course_slug_str = course_slug.to_string();
                let course_slug_str_clone = course_slug_str.clone();
                let url_str = url.to_string();
                let url_str_clone = url_str.clone();
                match download_type {
                            
                    "courseDownload" => {
                        let msg_clone = Arc::new(msg.msg.clone());

                        // 下载状态
                        let completed= serde_json::json!({
                            "type": "courseDownload",
                            "courseSlug": course_slug,
                            "status": "completed",
                            "percent": "100.0"
                        }).to_string();

                        // let process= serde_json::json!({
                        //     "type": "courseDownload",
                        //     "courseSlug": course_slug,
                        //     "status": "process",
                        //     "percent": "0"
                        // }).to_string();

                        let error= serde_json::json!({
                            "type": "courseDownload",
                            "courseSlug": course_slug,
                            "status": "error",
                            "percent": "0"
                        }).to_string();
                        

                        tokio::spawn(async move {
                            // 创建一个通道来通知进度发送任务停止
                            let (tx, mut rx) = tokio::sync::mpsc::channel::<()>(1); 

                            // 启动下载任务
                            course_download(download_progress.clone(), url_str_clone, course_slug_str_clone.clone(), tx).await;

                            // 等待1s
                            tokio::time::sleep(std::time::Duration::from_secs(1)).await;
                            // 启动一个任务来每秒发送进度消息
                            let server_clone = server.clone();
                            tokio::spawn(async move {
                                loop {
                                    // 等待1s
                                    tokio::time::sleep(std::time::Duration::from_secs(1)).await;

                                    // println!("正在下载 {}", course_slug_str_clone);
                                    // 检查是否收到停止信号
                                    if rx.try_recv().is_ok() {
                                        break;
                                    }
                                    let progress_map = download_progress.lock().unwrap();                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
                                    let progress = progress_map.get(&course_slug_str_clone).unwrap().clone();
                                    let percent = progress.progress.clone();

                                    if percent == -1.0 {
                                        // 发送失败消息
                                        server_clone.send_message(&String::from("download"), &error, 0);
                                    } else {
                                        // 保留两位小数
                                        let percent = format!("{:.2}", percent);
                                        let process= serde_json::json!({
                                            "type": "courseDownload",
                                            "courseSlug": course_slug_str_clone.clone(),
                                            "status": "process",
                                            "percent": percent
                                        }).to_string();
    
                                        // 发送进度消息
                                        server_clone.send_message(&String::from("download"), &process, 0);
                                    }
                                }

                                // 将课程信息写入数据库
                                let res2 = create_course_sections(course_slug_str, &conn).await;

                                // 发送下载成功或失败消息
                                if res2 {
                                    server_clone.send_message(&String::from("download"), &completed, 0);
                                }else {
                                    server_clone.send_message(&String::from("download"), &error, 0);
                                }
                            });
                            
                            // let res1 = post_course_download_course_zip(msg_clone.to_string()).await; 
                            
                            


                            
                            // 发送消息给相关会话
                            // if res1 && res2 {
                            //     server.send_message(&String::from("download"), &completed, 0);
                            // }else {
                            //     server.send_message(&String::from("download"), &error, 0);
                            // }
                             
                        });
                        
                    }
                    _ => {
                        println!("收到未处理的消息{:?}", msg.msg);

                    }
                    
                }

            }
            _ => {
                let server_clone = server.clone();
                server_clone.send_message(&String::from("local"), &msg.msg, 0);
                println!("收到未处理的通道中的消息{:?}", msg.msg);
            }
        }
    }
    // 下载进度
    let download_progress: Arc<Mutex<HashMap<String, context::ProgressData>>> =  Arc::new(Mutex::new(HashMap::new()));
    let download_progress_clone = download_progress.clone();
    // --- WebSocket ---
    // 初始化Websocket
    let websocket_server = WSServer::new(Arc::new(move |msg: &ClientMessage, server: &WSServer| {
        on_client_message(msg, server.clone().into(), conn_clone.clone(), download_progress.clone())
    })).start();



    // --- HTTP Server ---
    // Key是一个64字节的随机数，用于加密Cookie
    let secret_key = match cookie_key {
        Some(r) => {
            Key::from(r.as_bytes())
        },
        None => {
            // 生成一个Key长度64字节，内容只能是字母或者数字，不能包含其他字符
            let key = generate_key();

            // 写入到server.env尾部
            let teacher_env_file_path = get_path_in_exe_dir("server.env");
            let teacher_env_file_path = teacher_env_file_path.to_str().unwrap();
            let mut file = std::fs::OpenOptions::new().append(true).open(teacher_env_file_path).unwrap();
            file.write_fmt(format_args!("\nCOOKIE_KEY={}", String::from_utf8(key.clone()).unwrap())).unwrap();

            Key::from(key.as_slice())
        }
    };
    // --- UDPService ---
    // udp文件传输服务缓存
    let (sender, receiver) = mpsc::unbounded_channel::<FileTransmitPacket>();
    let receiver = Arc::new(tokio::sync::Mutex::new(receiver));

    // 初始化udp公告与发现服务
    let udp_server = UDPService::new(
        ALIAS.to_string(),
        INTERFACE_ADDR,
        MULTICAST_ADDR,
        MULTICAST_PORT,
        sender.clone(),
        receiver.clone()
    )
    .await;

    // 初始化udp文件传输服务
    let udp_file_server = UDPService::new(
        ALIAS.to_string(),
        INTERFACE_ADDR,
        MESSAGE_MULTICAST_ADDR,
        MESSAGE_MULTICAST_PORT,
        sender.clone(),
        receiver
    )
    .await;
    

    
    // 初始化应用状态
    let state = AppState { 
        ws_server: websocket_server.clone(), 
        udp_server: udp_server.clone(),
        udp_file_server: udp_file_server.clone(),
        conn,
        download_progress: download_progress_clone,

    };
    let state_clone = state.clone();
    // 初始化文件缓存
    let file_cache_map: Data<Mutex<HashMap<String, HttpFileCache>>> = Data::new(Mutex::new(HashMap::new()));

    // create server and try to serve over socket if possible
    let mut listenfd = ListenFd::from_env();
    let mut server = HttpServer::new(move || {
        App::new()
            .app_data(Data::new(state.clone()))
            .app_data(Data::new(websocket_server.clone()))
            .app_data(Data::clone(&file_cache_map))
            .wrap(middleware::Logger::default()) // enable logger
            .wrap(
                // create cookie based session middleware
                SessionMiddleware::builder(CookieSessionStore::default(), secret_key.clone())
                    .cookie_secure(false)
                    //session_lifecycle给session设置过期时间单位48小时
                    .session_lifecycle(PersistentSession::default().session_ttl(Duration::seconds(60 * 60 * 24 * 2)))
                    .build()
            )
            .default_service(web::route().to(not_found))
            .configure(init)
    });

    server = match listenfd.take_tcp_listener(0)? {
        Some(listener) => server.listen(listener)?,
        None => {
            let s = server.bind(&server_url);

            if s.is_err() {
                println!("氦星人教师机服务器尝试监听 {} 失败", server_url);
            }

            s?
        }
    };

    const SERVER_VERSION: &str = env!("CARGO_PKG_VERSION");
    println!("氦星人教师机服务器 {} 正在监听 {}", SERVER_VERSION, server_url);
    
    

    // 异步启动udp查找发现服务
    tokio::spawn(start_udp_server(state_clone.clone()));

    // 异步启动udp文件传输服务缓存接收
    tokio::spawn(start_file_message_recive(state_clone, sender.clone()));


    
    
    server.run().await?;

    Ok(())
}

// 启动UDP服务器的异步函数
async fn start_udp_server(
    app_state: context::AppState
) {
    let mut udp_server = app_state.udp_server.clone();
    let udp_file_server = app_state.udp_file_server.clone();
    let ws_server = app_state.ws_server;
    let conn: sea_orm::DatabaseConnection = app_state.conn;


    udp_file_server.socket
        .join_multicast_v4(udp_file_server.multicast_addr, udp_file_server.interface_addr)
        .expect("failed to join multicast");
    // 监听并广播组播消息
    udp_server.listen_and_announce_multicast(udp_server.receiver.clone(), udp_file_server, ws_server, conn).await;

}
// 启动udp文件传输服务缓存接收
async fn start_file_message_recive(
    app_state: context::AppState , 
    sender: mpsc::UnboundedSender<FileTransmitPacket>    
) {
    let mut udp_file_server = app_state.udp_file_server.clone();
    // 监听并广播组播消息
    // udp_file_server.file_message_recive(sender).await;
    
}



fn init(cfg: &mut web::ServiceConfig) {
    // WebSocket
    cfg.service(websocket_route);
    // udp 
    cfg.service(get_course_files);

    // 教师端
    cfg.service(get_section_teacher_login_config);

    // 教师端：远程接口
    // cfg.service(post_remote_train_plan_report);
    cfg.service(post_course_teacher_remote_login);//
    cfg.service(get_remote_all_teams_by_school_year);
    cfg.service(get_remote_team_users_by_userid);
    cfg.service(post_remote_team_users_sync);
    cfg.service(get_remote_all_trains_by_series);
    cfg.service(get_remote_all_series);
    // cfg.service(post_remote_train_sync);
    cfg.service(post_train_remote_update_check);
    cfg.service(post_train_remote_update_download);
    cfg.service(post_remote_train_sync_mp4);
    // cfg.service(post_remote_train_series_sync);
    cfg.service(post_remote_check_team_users_sync);

    // 教师端：本地接口
    cfg.service(section_teacher_login);
    cfg.service(put_user_password_by_ids);
    cfg.service(get_local_teams_by_school_year);
    // cfg.service(get_local_trains_by_school_year);
    cfg.service(get_local_users_by_team_id);
    // cfg.service(post_local_trainplan);
    // cfg.service(put_local_trainplan_start);
    // cfg.service(put_local_trainplan_end);
    // cfg.service(put_local_trainplan_reset);
    // cfg.service(put_local_trainplan_update);
    // cfg.service(put_local_trainplan_restore);
    // cfg.service(pub_local_trainplan_pause);
    // cfg.service(delete_trainplans_by_ids);
    // cfg.service(get_local_terminal);
    // cfg.service(delete_local_train);
    cfg.service(delete_local_team);
    // cfg.service(clear_student_records);
    cfg.service(get_online_count);
    // cfg.service(get_local_train_series);
    // cfg.service(get_student_answer_admin);

    cfg.service(get_terminals);
    cfg.service(get_all_teams);
    // cfg.service(reset_terminal_by_ips);
    cfg.service(delete_terminal_by_ip);
    cfg.service(clear_terminals);
    cfg.service(transfer_terminal);
    cfg.service(put_terminal_ip_bind_config);
    cfg.service(enable_no_password_login);
    cfg.service(enable_modern_style);
    cfg.service(enable_correction_mode);
    cfg.service(send_student_message);
    // cfg.service(put_train_user_record_admin);
    cfg.service(get_value_by_key);
    cfg.service(put_value_by_key);

    cfg.service(post_local_course);
    cfg.service(put_local_course_reset);
    cfg.service(get_course_by_config);
    cfg.service(get_file_content);
    cfg.service(get_section_result);// 获取学生节答题结果
    cfg.service(get_chapter_result);// 获取学生章答题结果
    cfg.service(export_course_progress_excel);// 导出学生答题进度
    cfg.service(start_class);// 开始上课（UDP）
    cfg.service(get_local_terminal);// 获取终端列表
    cfg.service(clear_terminals);// 清空终端列表
    cfg.service(delete_terminal_by_ip);// 删除终端
    cfg.service(get_section_record);// 班级记录
    cfg.service(get_statis_result);// 课程内班级统计结果
    cfg.service(open_fs);// 启动文件系统
    cfg.service(close_virtual_disk);// 关闭文件系统

    // 离线同步
    cfg.service(upload_train_file);
    cfg.service(upload_school_config);
    cfg.service(upload_users);
    cfg.service(upload_class_and_students);
    // cfg.service(upload_train);
    // cfg.service(upload_train_series);




    // 学生端
    cfg.service(get_server_ok); // 客户端获取服务器基本信息
    cfg.service(post_section_student_prelogin); // 学生预备登录
    cfg.service(post_section_student_login); // 学生登录
    cfg.service(get_train_student_session); // 教师机获取会话
    cfg.service(student_unbind_terminal); // 学生退出登录
    cfg.service(post_section_student_reset_password); // 学生重置密码
    // cfg.service(post_train_student_correction_record_create);   // 学生定制提交
    // cfg.service(post_train_student_correction_record_update);   // 学生更新订正记录
    // cfg.service(post_download_count); // 下载计数
    // cfg.service(get_download_mp4); // 下载MP4
    cfg.service(upload_operation_file); // 上传操作文件，应该未启用
    // cfg.service(get_trainplan_list_all); // 获取训练计划列表，订正用
    // cfg.service(get_train_student_plan_record); // 获取训练计划记录，订正用
    // cfg.service(get_plan_train_info); // 获取训练计划信息，订正用
    cfg.service(get_course_indics); // 获取课程目录
    cfg.service(update_course); // 更新课程
    cfg.service(update_course_chapter_rule);
    cfg.service(get_course_all);// 获取有权限的全部课程
    cfg.service(get_course_directory); //获取课程内容
    cfg.service(check_ipython);// 检查IPython环境是否正确
    cfg.service(init_ipython);// 初始化IPython环境
    cfg.service(run_ipython);// 运行IPython环境
    cfg.service(stop_ipython);// 停止IPython环境
    cfg.service(check_ipython_status);// 检查IPython环境状态
    cfg.service(prepare_fs);// 准备课程文件系统
    cfg.service(code_run);//用idle执行传入的代码
    // cfg.service(post_section_student_login);// 学生端登录
    // cfg.service(post_section_student_prelogin);// 学生端登录
    cfg.service(commit_source_block_record);  
    cfg.service(commit_ai_record);// 提交代码题记录  
    cfg.service(commit_oi_record);// 提交客观题记录
    cfg.service(commit_scratch_record);// 提交scratch题记录
    cfg.service(commit_oj_record);// 提交OJ题记录
    cfg.service(commit_microapp_record);// 提交微应用记录
    cfg.service(commit_ppt_record);// 提交PPT记录
    cfg.service(get_micro_app_primary_content_by_uuid);// 重置微应用文件
    cfg.service(commit_code_anwser_record);// 提交编程填空题记录
    cfg.service(commit_operation_record);// 提交操作operation记录
    cfg.service(get_commit_record);//获取提交记录
    cfg.service(query_section_content_and_type);// 获取章节内容
    cfg.service(get_user_session);// 获取用户缓存
    cfg.service(get_code_record);// 统计分析获取用户答题详情
    cfg.service(course_files_reset);// 文件重传
    cfg.service(save_scratch_record);// 保存Scratch题记录
    cfg.service(clear_student_record);// 清空用户答题记录
    
    
    cfg.service(get_config);//获取IP配置
    cfg.service(get_course_files);//提交传输文件请求
    // 文件下载
    cfg.service(static_files);
    cfg.service(static_files1);
    cfg.service(get_file_content_by_path);

    // 课程相关
    cfg.service(batch_delete_course);// 删除课程
    cfg.service(get_course_slug_list);// 获取数据库中存在的所有课程slug
    // cfg.service(post_course_download_course_zip);

    // 跨域接口
    // 课程文件下载
    // cfg.service(
    //     web::resource("api/admin/courseDownload")
    //         .wrap(
    //             Cors::permissive()
    //                 .allow_any_origin()
    //                 .allow_any_method()
    //                 .allow_any_header()
    //                 .expose_any_header()
    //                 .supports_credentials()
    //                 .max_age(3600)
    //         )
    //         .route(web::post().to(course_download))
    // );
    // 任务进度（暂时仅作课程下载进度查看）
    cfg.service(
        web::resource("api/admin/progress/{sha2_id}")
            .wrap(
                Cors::permissive()
                    .allow_any_origin()
                    .allow_any_method()
                    .allow_any_header()
                    .expose_any_header()
                    .supports_credentials()
                    .max_age(3600)
            )
            .route(web::get().to(get_download_progress))
    );
    
    // test
    cfg.service(test);
    cfg.service(test_websocket);
}

pub fn main(port:u16, cooke_key: Option<String>) {
    let result = start(port, cooke_key);

    if let Some(err) = result.err() {
        println!("Error: {}", err);
    }
}