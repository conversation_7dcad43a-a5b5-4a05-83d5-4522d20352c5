use core::str;
use std::{collections::{HashMap, HashSet}, fs, io::{ <PERSON><PERSON><PERSON>, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};
use sha2::{Digest, Sha256};
use tokio::time::timeout;
use encoding_rs::{UTF_8, GBK};

use actix_session::Session;
use actix_web::{get, post, put, web, HttpRequest, HttpResponse, HttpResponseBuilder, Result};
use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};
// use rquickjs::{Context, Runtime, Value};

use entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::SectionRecordItem};
use localsend::{protos::{TaskState, UDPMessageType}, server::UDPService};
use log::kv::source;

// use localsend::protos::TaskState;
use std::path::Path;

use std::time::UNIX_EPOCH;

use ring::digest;
use sea_orm::TransactionTrait;
use serde::{ Deserialize, Serialize};
use serde_json::{ json, Value};
use chrono:: prelude::*;
use websocket::server::ClientMessage;
use crate::controller_admin;

use super::context::{ AppState, BusinessError, BusinessResponse };
use service::{extract_zip, get_path_in_exe_dir, get_system_version, log_request, post_micro_app_file, post_scratch_file, CodeResult, HxrError, MicroAppCodeResult, Mutation, Query, NORMAL_TIME_OUT};

use tokio::fs::create_dir_all;



use std::fs::File;
use std::io::{BufReader, Read};
use std::sync::Arc;
use actix_http::StatusCode;
use futures_util::{SinkExt, StreamExt};
use quick_xml::events::Event;
use quick_xml::Reader;

use regex::Regex;
use tokio::io::AsyncWriteExt;


pub async fn get_course_path(
        slug: String,
        chapter: String,
        section: String,
    ) -> Result<String, BusinessError>  {
            // 获取对应course文件路径
    let course_path = get_path_in_exe_dir("course");
    let course_path = course_path.join(slug.clone()).join("course.json");
    
    // 解析目录文件，拼接对应字段
    let mut chapter_name = String::new();
    let mut section_name = String::new();
    
    let file_content = fs::read_to_string(course_path).expect("加载课程目录文件失败");
    let course_content: Value = serde_json::from_str(&file_content).unwrap();
    let mut course_type = String::new();
    let mut ext = String::new();
    
    //遍历course_content中的indics字段，获取必要信息
    for value in course_content["indics"].as_array().unwrap() {
        // 获取chapter_name
        if value["chapterName"] == chapter {
            chapter_name = format!("{}.{}", value["chapterIndex"],value["chapterName"]).replace("\"", "");
            // 获取section_name
            for section_value in value["sections"].as_array().unwrap() {
                if section_value["sectionName"] == section {
                    section_name = format!("{}.{}", section_value["sectionIndex"],section_value["sectionName"]).replace("\"", "");
                    ext = section_value["ext"].as_str().unwrap().to_string();
                    course_type = section_value["sectionType"].as_str().unwrap().to_string();
                }
            }
        }
    }
    // 获取课程内容文件
    // let if_show_answer = params.if_show_answer.clone();
    let content_path = get_path_in_exe_dir("course");
    let content_path = content_path.to_str().unwrap();
    let path = format!("{}/{}/{}/{}.{}", content_path,slug , chapter_name , section_name , ext);
    Ok(path)
}

//根据courseSlug获取课程目录
#[get("/api/web/course/{course_slug}/indices")]
pub async fn get_course_indics(
    slug: web::Path<String>,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError>  {
    let conn = &app_state.conn;
    //获取对应course文件路径
    let course_slug = slug.clone() ;
    let course_path = get_path_in_exe_dir("course");
    let course_path = course_path.to_str().unwrap();
    let path = format!("{}/{}/course.json", course_path, course_slug.clone());

    // let url = format!("http://127.0.0.1:7001/api/admin/course/getCourseAndSectionBycourseSlug/{}", &course_slug);
    let url = format!("http://127.0.0.1:7001/admin/course/getCourseAndSectionBycourseSlug/{}", &course_slug);

    //按照对应地址读取文件
    let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");

    //获取文件中的JSON数据
    let mut train_course = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    // 查询课程类型及创建者
    let type_and_id = match Query::course_find_by_slug_json(&txn, course_slug).await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("获取课程信息失败 {}", e.to_string()).to_string() });
        }
    };
    if let Some(params) = type_and_id {
        train_course["courseType"] = params["course_type"].clone().into();
        let creater = serde_json::from_str(params["creater"].as_str().unwrap()).unwrap();
        train_course["creater"] = creater;        
    }
    // 查询创建者的头像及姓名
    
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    let value = reqwest::Client::new()
        .get(url)
        .send()
        .await
        .unwrap()
        .json::<serde_json::Value>()
        .await
        .unwrap();

    let value = serde_json::json!({
        "createrID": 3013,
        "courseName": "氦星人机房教学系统演示课程",
        "courseSlug": "pltdemo",
        "courseDescription": null,
        "publish": 1,
        "indics": [
            {
                "collapse": false,
                "openTeam": [
                    163,
                    166,
                    169
                ],
                "sections": [
                    {
                        "ext": "ipynb",
                        "serial": null,
                        "status": true,
                        "sectionID": 14142,
                        "sectionName": "交互式课程",
                        "sectionType": "AI",
                        "sectionIndex": 1,
                        "sectionTitle": "交互式课程",
                        "created_at": "2024-09-03T01:09:38.000Z",
                        "updated_at": "2025-11-27T06:32:30.000Z",
                        "deleted_at": null
                    },
                    {
                        "ext": "slide",
                        "serial": null,
                        "status": true,
                        "sectionID": 14605,
                        "sectionName": "幻灯片课程",
                        "sectionType": "PPT",
                        "sectionIndex": 2,
                        "sectionTitle": "幻灯片课程",
                        "created_at": "2025-05-13T12:03:26.000Z",
                        "updated_at": "2025-05-17T12:03:26.000Z",
                        "deleted_at": null
                    },
                    // {
                    //     "ext": "json",
                    //     "serial": null,
                    //     "status": true,
                    //     "sectionID": 14144,
                    //     "sectionName": "图形化编程",
                    //     "sectionType": "Scratch",
                    //     "sectionIndex": 3,
                    //     "sectionTitle": "图形化编程",
                    //     "created_at": "2024-09-03T01:13:09.000Z",
                    //     "updated_at": "2025-05-13T12:03:37.000Z",
                    //     "deleted_at": null
                    // },
                    {
                        "ext": "json",
                        "serial": null,
                        "status": true,
                        "sectionID": 14145,
                        "sectionName": "MicroBit仿真课程",
                        "sectionType": "MicroBit",
                        "sectionIndex": 4,
                        "sectionTitle": "Micro:Bit仿真课程",
                        "created_at": "2024-09-03T01:13:25.000Z",
                        "updated_at": "2025-05-20T00:53:37.000Z",
                        "deleted_at": null
                    },
                    {
                        "ext": "xml",
                        "serial": null,
                        "status": true,
                        "sectionID": 14146,
                        "sectionName": "客观题",
                        "sectionType": "OI",
                        "sectionIndex": 5,
                        "sectionTitle": "客观题",
                        "created_at": "2024-09-29T06:31:14.000Z",
                        "updated_at": "2025-01-07T05:12:54.000Z",
                        "deleted_at": null
                    },
                    {
                        "ext": "json",
                        "serial": null,
                        "status": true,
                        "sectionID": 14147,
                        "sectionName": "Access操作题",
                        "sectionType": "Access",
                        "sectionIndex": 6,
                        "sectionTitle": "Access操作题",
                        "created_at": "2024-09-29T06:32:33.000Z",
                        "updated_at": "2025-05-20T08:16:22.000Z",
                        "deleted_at": null
                    },
                    {
                        "ext": "json",
                        "serial": null,
                        "status": true,
                        "sectionID": 14148,
                        "sectionName": "WPS表格操作题",
                        "sectionType": "Excel",
                        "sectionIndex": 7,
                        "sectionTitle": "WPS表格操作题",
                        "created_at": "2024-09-29T06:32:55.000Z",
                        "updated_at": "2024-09-29T06:33:02.000Z",
                        "deleted_at": null
                    },
                    {
                        "ext": "xml",
                        "serial": null,
                        "status": true,
                        "sectionID": 14149,
                        "sectionName": "在线编程评测题",
                        "sectionType": "OJ",
                        "sectionIndex": 8,
                        "sectionTitle": "在线编程评测题",
                        "created_at": "2024-09-29T06:33:31.000Z",
                        "updated_at": "2025-03-26T06:14:27.000Z",
                        "deleted_at": null
                    },
                    {
                        "ext": "code",
                        "serial": null,
                        "status": true,
                        "sectionID": 14151,
                        "sectionName": "编程填空题",
                        "sectionType": "CodeBlank",
                        "sectionIndex": 9,
                        "sectionTitle": "编程填空题",
                        "created_at": "2024-10-09T05:48:06.000Z",
                        "updated_at": "2024-10-09T05:48:43.000Z",
                        "deleted_at": null
                    }
                ],
                "chapterName": "演示课程",
                "chapterIndex": 1,
                "chapterTitle": "演示课程"
            }
        ],
        "courseType": "必修课",
        "saveCode": 1,
        "saveRunResult": 1,
        "allowPaste": 1,
        "allowCopy": 1,
        "questionAnswer": 1,
        "teams": [
            169,
            166
        ],
        "historyTeams": null,
        "teachers": [
            1,
            3013
        ],
        "containerInfo": {
            "image": "registry.cn-hangzhou.aliyuncs.com/haixr/myjupyter",
            "cpuLimit": 2,
            "memoryRequest": "8Gi"
        },
        "programLanguage": "Python",
        "updated_at": "2025-08-28T06:00:15.000Z",
        "deleted_at": null,
        "creater": {
            "name": "datouxia",
            "username": "datouxia",
            "avatar": null
        }
    });

    use serde_json::Value;
    use std::collections::HashMap;

    // 辅助函数：从 Value 中提取 sectionIndex 并转为 f64 用于排序
    fn parse_section_index(val: &Value) -> f64 {
        match val {
            Value::Number(n) => n.as_f64().unwrap_or(0.0),
            Value::String(s) => s.parse::<f64>().unwrap_or(0.0),
            _ => 0.0,
        }
    }

    fn compare_and_mark_sections(mut old_json: Value, new_json: Value) -> Value {
        let old_indics = old_json.get("indics").and_then(|v| v.as_array()).cloned();
        let new_indics = new_json.get("indics").and_then(|v| v.as_array()).cloned();
        println!("old_indics = {:#?} new_indics = {:#?}", old_indics, new_indics);

        if let (Some(old_indics), Some(new_indics)) = (old_indics, new_indics) {
            // 构建 new sections 的 map: sectionID -> (chapter_index, section)
            let mut new_sections_map: HashMap<u64, (&Value, &Value)> = HashMap::new();
            for chapter in new_indics.iter() {
                if let Some(sections) = chapter.get("sections").and_then(|s| s.as_array()) {
                    for sec in sections {
                        if let Some(id) = sec.get("sectionID").and_then(|id| id.as_u64()) {
                            new_sections_map.insert(id, (chapter, sec));
                        }
                    }
                }
            }

            // 修改 old 的 sections，标记状态
            if let Some(old_indics_arr) = old_json.get_mut("indics").and_then(|v| v.as_array_mut()) {
                for chapter in old_indics_arr.iter_mut() {
                    if let Some(sections) = chapter.get_mut("sections").and_then(|s| s.as_array_mut()) {
                        for sec in sections.iter_mut() {
                            let section_id = sec.get("sectionID").and_then(|id| id.as_u64());

                            let status = if let Some(id) = section_id {
                                // 1. 如果 deletedAt 不为空 → 删除
                                if sec.get("deleted_at").is_some_and(|v| !v.is_null()) {
                                    "删除".to_string()
                                }
                                // 2. 如果在 new 中不存在 → 删除（逻辑删除）
                                else if !new_sections_map.contains_key(&id) {
                                    "删除".to_string()
                                }
                                // 3. 如果存在，比较 updatedAt
                                else if let Some((_, new_sec)) = new_sections_map.get(&id) {
                                    let old_updated = sec.get("updated_at");
                                    let new_updated = new_sec.get("updated_at");
                                    if old_updated != new_updated {
                                        "更新".to_string()
                                    } else {
                                        // 未变化，保留或设为“无变化”
                                        sec.get("modify_status")
                                            .and_then(|s| s.as_str())
                                            .unwrap_or("无变化")
                                            .to_string()
                                    }
                                } else {
                                    "未知".to_string()
                                }
                            } else {
                                "未知".to_string()
                            };

                            println!("sec = {:#?}", sec);

                            sec.as_object_mut().unwrap().insert("modify_status".to_string(), Value::String(status));
                        }

                        // 对当前章节的 sections 按 sectionIndex 排序
                        sections.sort_by(|a, b| {
                            let a_idx = parse_section_index(a.get("sectionIndex").unwrap_or(&Value::Null));
                            let b_idx = parse_section_index(b.get("sectionIndex").unwrap_or(&Value::Null));
                            a_idx.partial_cmp(&b_idx).unwrap_or(std::cmp::Ordering::Equal)
                        });
                    }
                }

                // 处理新增的 sections（在 new 中有，old 中没有）
                for chapter in new_indics {
                    let new_chapter_name = chapter.get("chapterName").and_then(|n| n.as_str()).unwrap_or("");
                    if let Some(new_sections) = chapter.get("sections").and_then(|s| s.as_array()) {
                        for new_sec in new_sections {
                            let id = new_sec.get("sectionID").and_then(|id| id.as_u64());
                            if let Some(id) = id {
                                // 检查是否已在 old 中存在
                                let exists_in_old = old_indics.iter().any(|old_chap| {
                                    old_chap
                                        .get("sections")
                                        .and_then(|secs| secs.as_array())
                                        .map_or(false, |secs| {
                                            secs.iter().any(|s| {
                                                s.get("sectionID").and_then(|sid| sid.as_u64()) == Some(id)
                                            })
                                        })
                                });

                                if !exists_in_old {
                                    // 是新增的 section
                                    let mut new_sec_clone = new_sec.clone();
                                    new_sec_clone
                                        .as_object_mut()
                                        .unwrap()
                                        .insert("modify_status".to_string(), Value::String("新增".to_string()));

                                    // 找到 old 中对应的 chapter（按 chapterName）
                                    if let Some(target_chapter) = old_indics_arr.iter_mut().find(|c| {
                                        c.get("chapterName").and_then(|n| n.as_str()) == Some(new_chapter_name)
                                    }) {
                                        if let Some(secs) = target_chapter.get_mut("sections").and_then(|s| s.as_array_mut()) {
                                            secs.push(new_sec_clone);
                                        }
                                    } else {
                                        // 如果 chapter 不存在，可以选择跳过或创建新 chapter
                                        // 此处按需求：假设 chapter 不会新增，仅处理 section 新增
                                        // 如需支持 chapter 新增，可在此处 push 新 chapter
                                    }
                                }
                            }
                        }
                    }
                }

                // 再次对所有章节的 sections 排序（因为新增了 section）
                for chapter in old_indics_arr {
                    if let Some(sections) = chapter.get_mut("sections").and_then(|s| s.as_array_mut()) {
                        sections.sort_by(|a, b| {
                            let a_idx = parse_section_index(a.get("sectionIndex").unwrap_or(&Value::Null));
                            let b_idx = parse_section_index(b.get("sectionIndex").unwrap_or(&Value::Null));
                            a_idx.partial_cmp(&b_idx).unwrap_or(std::cmp::Ordering::Equal)
                        });
                    }
                }
            }
        }

        old_json
    }

    println!("value = {:#?}", value);

    let train_course = compare_and_mark_sections(train_course, value);

    println!("train_course = {:#?}", train_course);

    // 返回 JSON 数据
    // Ok(HttpResponse::Ok().json(train_course))
    BusinessResponse::ok(train_course).to_json_result()
}



#[get("/api/test/websocket")]
pub async fn test_websocket() -> Result<HttpResponse, BusinessError> {
    // 启动46046端口的WebSocket服务器用于测试
    tokio::spawn(async move {
        let listener = match tokio::net::TcpListener::bind("127.0.0.1:46046").await {
            Ok(listener) => listener,
            Err(e) => {
                log::error!("Failed to bind to port 46046: {}", e);
                return;
            }
        };

        println!("Test WebSocket server listening on 127.0.0.1:46046");

        while let Ok((stream, _)) = listener.accept().await {
            tokio::spawn(async move {
                let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel();

                // 模拟进度更新
                let progress_task = tokio::spawn(async move {
                    for i in 0..=100u8 {
                        if tx.send(i).is_err() {
                            break;
                        }
                        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    }
                });

                // 处理WebSocket连接
                match tokio_tungstenite::accept_async(stream).await {
                    Ok(ws_stream) => {
                        let (mut writer, _reader) = ws_stream.split();

                        // 发送进度更新
                        while let Some(progress) = rx.recv().await {
                            let message = tokio_tungstenite::tungstenite::Message::Text(progress.to_string());
                            if writer.send(message).await.is_err() {
                                break;
                            }

                            // 如果进度达到100%，关闭连接
                            if progress >= 100u8 {
                                let _ = writer.close().await;
                                break;
                            }
                        }
                    }
                    Err(e) => {
                        log::warn!("Failed to accept WebSocket connection: {}", e);
                    }
                }

                // 等待进度任务完成
                let _ = progress_task.await;
            });
        }
    });

    BusinessResponse::ok("WebSocket test server started").to_json_result()
}

#[put("/api/web/course/{slug}")]
pub async fn update_course(slug: web::Path<String>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    let course_slug = slug.into_inner();
    let client = reqwest::Client::builder().cookie_store(true).build().map_err(|e| BusinessError::InternalError { reason: e.to_string() })?;

    let base_url = "http://127.0.0.1:7001";

    // 调用 GET /api/admin/user/adminSession 获取管理员会话，获取其中的csrfToken
    // let session_url = String::from(url.clone()) + "/api/admin/user/adminSession";
    let session_url = format!("{}/admin/user/adminSession", base_url);

    let mut res = match client
        .get(&session_url)
        .timeout(Duration::from_secs(10))
        .send()
        .await
        .map_err(|e| BusinessError::InternalError { reason: e.to_string() })?
        .json::<serde_json::Value>()
        .await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason :  format!("在请求{}, 网络出错 {}", session_url, e.to_string().as_str()) });
        }
    };

    let csrf = if res["code"] == serde_json::Value::from(400) {
        let map = Query::system_config_find_by_keys(&app_state.conn, vec!["default_username".into(), "default_password".into()])
            .await
            .map_err(|e| BusinessError::InternalError { reason: e.to_string() })?;
        let body = serde_json::json!({
            "username": map.get("default_username"),
            "password": map.get("default_password"),
            "isWeakPassword": true,
            "_csrf": &res["csrfToken"]
        });
        let mut res = match client
            .post(format!("{}/admin/user/adminLogin", base_url))
            .json(&body)
            .timeout(Duration::from_secs(10))
            .send()
            .await
            .map_err(|e| BusinessError::InternalError { reason: e.to_string() })?
            .json::<serde_json::Value>()
            .await {
            Ok(r) => r,
            Err(e) => {
                return Err(BusinessError::InternalError { reason : format!("在请求{}, 网络出错 {}", "/admin/user/adminLogin", e.to_string().as_str()) });
            }
        };
        res["csrfToken"].take()
    } else {
        res["csrfToken"].take()
    };

    // 直接调用本地API获取课程下载URL，不依赖Faye
    let json = json!({
        "courseSlug": &course_slug,
        "_csrf": csrf
    });
    println!("json = {:#?}", json);

    let response = client
        .post(format!("{}/admin/file/updateCourseContentForPLT", base_url))
        .json(&json)
        .send()
        .await
        .map_err(|e| BusinessError::InternalError { reason: e.to_string() })?
        .json::<serde_json::Value>()
        .await
        .map_err(|e| BusinessError::InternalError { reason: e.to_string() })?;

    println!("updateCourseContentForPLT response = {:#?}", response);

    // 检查响应是否成功
    if response["code"] != 0 {
        return Err(BusinessError::InternalError {
            reason: format!("课程更新失败: {}", response["message"].as_str().unwrap_or("未知错误"))
        });
    }

    // 从响应中获取下载URL
    let download_url = response["data"]["url"].as_str()
        .ok_or_else(|| BusinessError::InternalError {
            reason: "无法获取课程下载URL".to_string()
        })?;

    // 准备下载路径
    let url = Arc::new(download_url.to_string());
    let course_dir = get_path_in_exe_dir("course");
    let course_zip = course_dir.join(format!("{}.zip", course_slug));
    let course_file_path = course_dir.join(format!("{}", course_slug));
    let output_path = Arc::new(course_zip);
    let course_file_path = Arc::new(course_file_path);

    // 启动46046端口的WebSocket服务器
    tokio::spawn(async move {
        let listener = match tokio::net::TcpListener::bind("127.0.0.1:46046").await {
            Ok(listener) => listener,
            Err(e) => {
                log::error!("Failed to bind to port 46046: {}", e);
                return;
            }
        };

        println!("Course update WebSocket server listening on 127.0.0.1:46046");

        while let Ok((stream, _)) = listener.accept().await {
            let url1 = url.clone();
            let path = output_path.clone();
            let course_file_path1 = course_file_path.clone();

            tokio::spawn(async move {
                let (tx4, mut rx4) = tokio::sync::mpsc::unbounded_channel();

                // 启动下载和解压任务
                let download_task = tokio::spawn(async move {
                    if let Err(e) = download_and_extract_with_progress(&url1, &path, &course_file_path1, tx4).await {
                        log::warn!("Failed to download or extract: {}", e);
                    }
                });

                // 处理WebSocket连接
                match tokio_tungstenite::accept_async(stream).await {
                    Ok(ws_stream) => {
                        let (mut writer, _reader) = ws_stream.split();

                        // 发送进度更新
                        while let Some(progress) = rx4.recv().await {
                            let message = tokio_tungstenite::tungstenite::Message::Text(progress.to_string().into());
                            if writer.send(message).await.is_err() {
                                break;
                            }

                            // 如果进度达到100%，关闭连接
                            if progress >= 100u8 {
                                let _ = writer.close().await;
                                break;
                            }
                        }
                    }
                    Err(e) => {
                        log::warn!("Failed to accept WebSocket connection: {}", e);
                    }
                }

                // 等待下载任务完成
                let _ = download_task.await;
            });
        }
    });

    BusinessResponse::ok(response).to_json_result()
}


pub async fn download_and_extract_with_progress(
    url: &str,
    output_path: &PathBuf,
    course_file_path: &PathBuf,
    progress_tx: tokio::sync::mpsc::UnboundedSender<u8>,
) -> Result<(), Box<dyn std::error::Error>> {
    let url = url.trim();
    if url.is_empty() {
        return Err("URL is empty".into());
    }

    let download_total = 85.0;

    let client = reqwest::Client::builder()
        .connect_timeout(Duration::from_secs(10))
        .timeout(Duration::from_secs(60))
        .user_agent("Rust Downloader")
        .build()?;

    let resp = client.get(url).send().await?;
    let total_size = resp
        .headers()
        .get(reqwest::header::CONTENT_LENGTH)
        .and_then(|ct_len| ct_len.to_str().ok())
        .and_then(|ct_len| ct_len.parse::<u64>().ok())
        .unwrap_or(0);

    // 如果没有总大小，无法计算百分比，可以提前返回错误或只发 0/100
    if total_size == 0 {
        eprintln!("Warning: Content-Length not provided, cannot report percentage progress.");
        // 可选：发送 0 表示开始，100 表示结束
        let _ = progress_tx.send(0u8);
    }

    let mut file = tokio::fs::File::create(&output_path).await?;
    let mut stream = resp.bytes_stream();
    let mut downloaded: u64 = 0;
    let mut last_percent: u8 = 0; // 避免频繁发送相同百分比

    while let Some(chunk) = stream.next().await {
        let chunk = chunk?;
        downloaded += chunk.len() as u64;
        file.write_all(&chunk).await?;

        if total_size > 0 {
            let percent = (downloaded as f64 / total_size as f64 * download_total).min(download_total) as u8;
            // 只有进度变化时才发送（避免刷屏）
            if percent != last_percent {
                let _ = progress_tx.send(percent);
                last_percent = percent;
            }
        }
    }

    // 解压课程文件
    let _ = extract_zip(&output_path, &course_file_path)?;
    // 确保最后发送 100%
    let _ = progress_tx.send(100u8);


    Ok(())
}


// 获取课程内容
// oi解析结构
#[derive(Serialize,Debug,Clone)]
struct Options {
    key: String,
    image: String,
    text: String,
}

#[derive(Serialize,Debug,Clone)]
struct Answer {
    key: String,
    text: Vec<String>,
}

#[derive(Serialize,Debug)]
struct Question {
    index: String,
    UUID: String,
    content: String,
    options: Vec<Options>,
    questionType: String,
    answer: serde_json::Value,
}

#[derive(Serialize,Debug)]
struct OIData {
    r#type: String,
    content: Vec<Question>,
    title: String,
    stat: HashMap<String, serde_json::Value>,
    if_show_answer: String,
    if_submit_limit: String,
    submit_limit: String,
    status: bool,
}
// oj解析结构
#[derive(Deserialize, Serialize,Debug)]
struct OJData {
    pub r#type: String,
    pub content: OJContent,
    pub stat: HashMap<String, serde_json::Value>,
    pub status: bool,
}
#[derive(Deserialize, Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct OJContent {
    pub content: String,
    pub data_range_prompts: String,
    pub in_format: String,
    pub in_out_examples: Vec<Example>,
    pub out_format: String,
    pub question_type: String,
    pub title: String,
    pub time_limit: String,
    pub memory_limit: String,
    pub default_code: String,
    pub judge_menu: JudgeMenu,
    pub tags: Vec<String>,
    pub example_solutions: ExampleSolutions,
    
}
#[derive(Deserialize, Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct Example {
    pub in_example: String,
    pub out_example: String,
    pub description: String,
}
#[derive(Deserialize, Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct JudgeMenu {
    pub test_files: Vec<TestFile>,
    pub file: Vec<String>,
    pub spj_files: Vec<String>,
}#[derive(Deserialize, Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct ExampleSolutions {
    pub open_solution: String,
    pub solution_list: Vec<Solution>,
}
#[derive(Deserialize, Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct TestFile {
    pub r#type: String,
    pub file_name: String,
    pub r#in: String,
    pub out: String,
}
#[derive(Deserialize, Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct Solution {
    pub index: u32,
    pub code: String,
    pub publish_user: String,
    pub note: String,
    pub time: String,
}
#[get("/api/web/course/{courseSlug}/directory/{chapterName}/section/{sectionName}")]
pub async fn get_course_directory(
    params: web::Path<(String,  String,  String)>,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>  {
    let conn = &app_state.conn;

    // 获取对应课程文件路径
    let (slug , chapter , section) = params.into_inner();
    let mut course_type:String = "".to_string();
    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    // 查询课程类型及创建者
    let type_info = match Query::course_find_by_slug_json(&txn, slug.clone()).await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("获取课程信息失败 {}", e.to_string()).to_string() });
        }
    };
    if let Some(params) = type_info {
        course_type = params["course_type"].to_string();
    }
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    
    // 获取对应course文件路径
    let course_path = get_path_in_exe_dir("course");
    let course_path = course_path.join(slug.clone()).join("course.json");
    
    // 解析目录文件，拼接对应字段
    let mut chapter_name = String::new();
    let mut section_name = String::new();
    
    let file_content = fs::read_to_string(course_path).expect("加载课程目录文件失败");
    let course_content: Value = serde_json::from_str(&file_content).unwrap();
    let mut course_type = String::new();
    let mut ext = String::new();
    
    //遍历course_content中的indics字段，获取必要信息
    for value in course_content["indics"].as_array().unwrap() {
        // 获取chapter_name
        if value["chapterName"] == chapter {
            chapter_name = format!("{}.{}", value["chapterIndex"],value["chapterName"]).replace("\"", "");
            // 获取section_name
            for section_value in value["sections"].as_array().unwrap() {
                if section_value["sectionName"] == section {
                    section_name = format!("{}.{}", section_value["sectionIndex"],section_value["sectionName"]).replace("\"", "");
                    ext = section_value["ext"].as_str().unwrap().to_string();
                    course_type = section_value["sectionType"].as_str().unwrap().to_string();
                }
            }
        }
    }
    // 获取课程内容文件
    // let if_show_answer = params.if_show_answer.clone();
    let content_path = get_path_in_exe_dir("course");
    let content_path = content_path.to_str().unwrap();
    let path = format!("{}/{}/{}/{}.{}", content_path,slug , chapter_name , section_name , ext);

    // 按照对应地址读取文件
    let mut train_course: Value = serde_json::Value::Array(Vec::new());
    // 获取文件中的JSON数据
    // match course_type.as_str() {
    //     "OI" => {
    //         // match execute_node_script("service/scripts/process_questions.js", &path, false) {
    //         //     Ok(result) => println!("Script result: {}", result),
    //         //     Err(e) => eprintln!("Error: {}", e),
    //         // }
    //         let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
    //         let file_content = file_content.replace("../assets/", &format!("file/course/{}/assets/", slug.clone()));
    //         let mut reader = Reader::from_reader(Cursor::new(file_content));
    //         reader.trim_text(true);
    //         let mut buf = Vec::new();
    //         let mut questions = Vec::new();
    //         let mut title = String::new();
    //         let mut if_show_answer = String::new();
    //         let mut current_options = Vec::new();
    //         let mut current_answer = None;
    //         let mut current_choice_answer = None;
    //         let mut current_content = String::new();
    //         let mut current_uuid = String::new();
    //         let mut current_index = String::new();
    //         let mut current_question_type = String::new();
    //         let mut current_name = String::new();
    //         let mut key = String::new();
    //         let mut image = String::new();

            
    //         let mut element_stack = Vec::new();

    //         loop {
    //             match reader.read_event(&mut buf) {
    //                 Ok(Event::Start(ref e)) => {
    //                     element_stack.push(str::from_utf8(e.name()).unwrap().to_string());
    //                     let c_name = e.name();
    //                     let current_name = str::from_utf8(c_name).unwrap();
    //                     match e.name() {
    //                         b"singleChoice" => {
    //                             for attr in e.attributes() {
    //                                 let attr = attr.unwrap();
    //                                 match attr.key {
    //                                     b"UUID" => current_uuid = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     b"index" => current_index = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     b"text" => current_content = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     b"answer" => current_choice_answer = Some(str::from_utf8(&attr.value).unwrap().to_string()),
    //                                     _ => (),
    //                                 }
    //                             }
    //                             current_question_type = "单选题".to_string();
    //                         },
    //                         b"content" => {
    //                             for attr in e.attributes() {
    //                                 let attr = attr.unwrap();
    //                                 match attr.key {
    //                                     b"UUID" => current_uuid = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     b"index" => current_index = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     _ => (),
    //                                 }
    //                             }
    //                             current_question_type = "文本".to_string();
    //                         },
    //                         b"completion" => {
    //                             for attr in e.attributes() {
    //                                 let attr = attr.unwrap();
    //                                 match attr.key {
    //                                     b"UUID" => current_uuid = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     b"index" => current_index = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     b"text" => {
    //                                         let text = str::from_utf8(&attr.value).unwrap().to_string();
    //                                         let re = regex::Regex::new(r"【__([^【】]*)__】").unwrap();
    //                                         current_content = re.replace(&text, "【____】").to_string();
    //                                         if let Some(captures) = re.captures(&text) {
    //                                             if let Some(matched) = captures.get(1) {
    //                                                 current_answer = Some(Answer {
    //                                                     key: "1".to_string(),
    //                                                     text: vec![matched.as_str().to_string()],
    //                                                 });
    //                                             }
    //                                         }
    //                                     },
    //                                     _ => (),
    //                                 }
    //                             }
    //                             current_question_type = "填空题".to_string();
    //                         },
    //                         b"jumbledSentence" => {
    //                             for attr in e.attributes() {
    //                                 let attr = attr.unwrap();
    //                                 match attr.key {
    //                                     b"UUID" => current_uuid = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     b"index" => current_index = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     b"text" => current_content = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     _ => (),
    //                                 }
    //                             }
    //                             current_question_type = "选择填空题".to_string();
    //                         },
    //                         b"option" => {
    //                             for attr in e.attributes() {
    //                                 let attr = attr.unwrap();
    //                                 match attr.key {
    //                                     b"key" => key = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     b"image" => image = str::from_utf8(&attr.value).unwrap().to_string(),
    //                                     _ => (),
    //                                 }
    //                             }
    //                         },
    //                         b"answer" => {
    //                             for attr in e.attributes() {
    //                                 let attr = attr.unwrap();
    //                                 if attr.key == b"key" {
    //                                     key = str::from_utf8(&attr.value).unwrap().to_string();
    //                                 }
    //                             }
    //                         },
    //                         _ => (),
    //                     }
    //                 },
    //                 Ok(Event::CData(e)) => {
    //                     if let Some(last_element) = element_stack.last() {
    //                         match last_element.as_str() {
    //                             "title" => {
    //                                 title = str::from_utf8(&e.into_inner()).unwrap().to_string();
    //                             },
    //                             "ifShowAnswer" => {
    //                                 if_show_answer = str::from_utf8(&e.into_inner()).unwrap().to_string();
    //                             },
    //                             "option" => {
    //                                 let text = str::from_utf8(&e.into_inner()).unwrap().to_string();
    //                                 current_options.push(Options {
    //                                     key: key.clone(),
    //                                     image: image.clone(),
    //                                     text,
    //                                 });
    //                             },
    //                             "content" => {
    //                                 current_content += str::from_utf8(&e.into_inner()).unwrap();
    //                             },
    //                             "answer" => {
    //                                 let text = str::from_utf8(&e.into_inner()).unwrap().to_string();
    //                                 current_answer = Some(Answer {
    //                                     key: key.clone(),
    //                                     text: vec![text],
    //                                 });
    //                             },
    //                             _ => {}
    //                         }
    //                     }
    //                 },
    //                 Ok(Event::Text(e)) => {
    //                     if let Some(last_element) = element_stack.last() {
    //                         if last_element == "ifShowAnswer" {
    //                             if_show_answer = str::from_utf8(&e.into_inner()).unwrap().to_string();
    //                         } else if last_element == "content" {
    //                             current_content += str::from_utf8(&e.into_inner()).unwrap();
    //                         }
    //                     }
    //                 },
    //                 Ok(Event::End(ref e)) => {
    //                     if let Some(last_element) = element_stack.pop() {
    //                         match last_element.as_str() {
    //                             "singleChoice" | "content" | "completion" | "jumbledSentence" => {
    //                                 let answer = match current_question_type.as_str() {
    //                                     "单选题" => {
    //                                         if let Some(answer) = current_choice_answer.clone() {
    //                                             serde_json::Value::String(answer)
    //                                         } else {
    //                                             serde_json::Value::Null
    //                                         }
    //                                     },
    //                                     "填空题" => {
    //                                         if let Some(answer) = current_answer.clone() {
    //                                             serde_json::Value::Array(vec![serde_json::json!({
    //                                                 "key": 1,
    //                                                 "text": answer.text
    //                                             })])
    //                                         } else {
    //                                             serde_json::Value::Array(Vec::new())
    //                                         }
    //                                     },
    //                                     "选择填空题" => serde_json::Value::Object(serde_json::Map::new()),
    //                                     _ => serde_json::Value::Null,
    //                                 };
    //                                 questions.push(Question {
    //                                     index: current_index.clone(),
    //                                     UUID: current_uuid.clone(),
    //                                     content: current_content.clone(),
    //                                     options: current_options.clone(),
    //                                     questionType: current_question_type.clone(),
    //                                     answer,
    //                                 });
    //                                 current_options.clear();
    //                                 current_answer = None;
    //                             },
    //                             _ => (),
    //                         }
    //                     }
    //                 },
    //                 Ok(Event::Eof) => break,
    //                 Err(e) => panic!("Error at position {}: {:?}", reader.buffer_position(), e),
    //                 _ => (),
    //             }
    //             buf.clear();
    //         }
               

    //         let data = OIData {
    //             r#type: "OI".to_string(),
    //             content: questions,
    //             title,
    //             stat: HashMap::new(),
    //             if_show_answer: if_show_answer,
    //             if_submit_limit: "".to_string(),
    //             submit_limit: "".to_string(),
    //             status: true,
    //         };
    //         return BusinessResponse::ok(data).to_json_result();
            

    //     }
    //     "OJ" => {
    //         let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
    //         let mut reader = Reader::from_reader(Cursor::new(file_content));
    //         reader.trim_text(true);
    //         let mut buf = Vec::new();
    //         let mut current_element = String::new();
    //         let mut in_example = String::new();
    //         let mut out_example = String::new();
    //         let mut publish_user = String::new();
    //         let mut note = String::new();
    //         let mut time = String::new();
    //         let mut input_info = String::new();
    //         let mut output_info = String::new();
    //         let mut test_count = 1;
    //         // 创建返回内容的容器
    //         let mut content = OJContent {
    //             content: String::new(),
    //             data_range_prompts: String::new(),
    //             in_format: String::new(),
    //             in_out_examples: Vec::new(),
    //             out_format: String::new(),
    //             question_type: String::new(),
    //             title: String::new(),
    //             time_limit: String::new(),
    //             memory_limit: String::new(),
    //             default_code: String::new(),
    //             judge_menu: JudgeMenu {
    //                 test_files: Vec::new(),
    //                 file: Vec::new(),
    //                 spj_files: Vec::new(),
    //             },
    //             tags: Vec::new(),
    //             example_solutions: ExampleSolutions {
    //                 open_solution: String::new(),
    //                 solution_list: Vec::new(),
    //             },
    //         };
    //         loop {
    //             match reader.read_event(&mut buf) {
    //                 Ok(Event::Start(ref e)) =>{
    //                     current_element = String::from_utf8_lossy(e.name()).to_string();

    //                     if e.name() == b"solution" {
    //                         publish_user =  String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[0].clone()).to_string();
    //                         note = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[2].clone()).to_string();
    //                         time = String::from_utf8_lossy(&e.attributes().map(|a| a.unwrap().value.into_owned()).collect::<Vec<_>>()[1].clone()).to_string();
    //                 }
    //                 },
    //                 // 处理cdata中的内容
    //                 Ok(Event::CData(e)) => {
       
    //                     match current_element.as_str() {
    //                         "title" => {
    //                             content.title = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                         },
    //                         "time_limit" => {
    //                             content.time_limit = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                         },
    //                         "memory_limit" => {
    //                             content.memory_limit = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                         },
    //                         "description" => {
    //                             content.content = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                         },
    //                         "input" => {
    //                             content.in_format = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                         },
    //                         "output" => {
    //                             content.out_format = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                                                     }
    //                         "sample_input" => {
    //                             in_example = String::from_utf8_lossy(&e.into_inner()).to_string();   
    //                         }
    //                         "sample_output" => {
    //                             out_example = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                         }
    //                         "sample_description" => {
    //                             let description = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                             let in_example = in_example.clone();
    //                             let out_example = out_example.clone();
    //                             content.in_out_examples.push(Example {
    //                                 in_example,
    //                                 out_example,
    //                                 description,
    //                             });
    //                         }
    //                         "question_type" => {
    //                             content.question_type = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                         },
    //                         "hint" => {
    //                             content.data_range_prompts = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                         },
    //                         "solution" => {
    //                             let code = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                             let publish_user =  publish_user.clone();
    //                             let note = note.clone();
    //                             let time = time.clone();
    //                             content.example_solutions.solution_list.push(Solution {
    //                                         index: 1,
    //                                         code,
    //                                         publish_user,
    //                                         note,
    //                                         time,
    //                                     });
    //                         }
    //                         "test_input" => {
    //                             input_info = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                         }
    //                         "test_output" => {
    //                             output_info = String::from_utf8_lossy(&e.into_inner()).to_string();
    //                         }
    //                         _ => {}
    //                     }
    //                 }
    //                 Ok(Event::End(e)) => {
    //                     if  e.name()== b"test_output" {
    //                         // 创建目录
    //                         let file_path = get_path_in_exe_dir("data").join(slug.clone()).join(chapter.clone()).join(section.clone());
    //                         let _ = create_dir_all(&file_path).await;
    //                         // 写入input文件
    //                         let input_file_path = file_path.join(format!("{}.in", test_count.to_string()));
    //                         let mut input_file = File::create(input_file_path.clone()).expect("Unable to create file");
    //                         input_file.write_all(input_info.as_bytes()).expect("Unable to write data");
    //                         // 写入output文件
    //                         let output_file_path = file_path.join(format!("{}.out", test_count.to_string()));
    //                         let mut output_file = File::create(output_file_path.clone()).expect("Unable to create file");
    //                         output_file.write_all(output_info.as_bytes()).expect("Unable to write data");
    //                         // 修改格式
    //                         let input_file_path = input_file_path.to_str().unwrap().to_string();
    //                         let output_file_path = output_file_path.to_str().unwrap().to_string();
    //                         content.judge_menu.test_files.push(TestFile {
    //                             r#type: "inout".to_string() ,
    //                             file_name: test_count.to_string(),
    //                             r#in: input_file_path,
    //                             out: output_file_path,
    //                         });
                            
    //                         test_count += 1;
    //                     }
                        
    //                 }
                    
    //                 Ok(Event::Eof) => break,
    //                 _ => (),
    //             }
    //             buf.clear();
    //         }
        
    //         let oj = OJData {
    //             r#type: "OJ".to_string(),
    //             content,
    //             stat: HashMap::new(),
    //             status: true,
    //         };
    //         return BusinessResponse::ok(oj).to_json_result();


            
    //     }
    //     _ => {
    //         let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
    //         train_course = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
    //     }
    // }
    // 对oj题做特殊处理
    // if course_type == "OJ" {
    //     let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
    //     train_course = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");

    // }
    let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
    train_course = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
    // 插入课程类型
    match train_course {
        Value::Object(ref mut object) => {
            // 如果 train_course 是一个对象，直接添加新属性
            object.insert("type".to_string(), serde_json::Value::String(course_type));
        }
        _ => {
            
        }
    }
    
    // 返回JSON数据
    BusinessResponse::ok(train_course).to_json_result()
}


// 拼接课程内容文件的路径
fn get_path(course_slug:String,chapter_name:String,section_name:String)->String{
        // 获取对应course文件路径
        let course_path = get_path_in_exe_dir("course");
        let course_path = course_path.join(course_slug.clone()).join("course.json");
        // 解析目录文件，拼接对应字段
        let mut chapter_name_join = String::new();
        let mut section_name_join = String::new();
    
        let file_content = fs::read_to_string(course_path).expect("加载课程目录文件失败");
        let course_content: Value = serde_json::from_str(&file_content).unwrap();
        let mut ext = String::new();
        //遍历course_content中的indics字段，获取必要信息
        for value in course_content["indics"].as_array().unwrap() {
            // 获取chapter_name
            if value["chapterName"] == chapter_name {
                chapter_name_join = format!("{}.{}", value["chapterIndex"],value["chapterName"]).replace("\"", "");
                // 获取section_name
                for section_value in value["sections"].as_array().unwrap() {
                    if section_value["sectionName"] == section_name {
                        section_name_join = format!("{}.{}", section_value["sectionIndex"],section_value["sectionName"]).replace("\"", "");
                        ext = section_value["ext"].as_str().unwrap().to_string();
                    }
                }
            }
        }
        // 获取课程内容文件
        let content_path = get_path_in_exe_dir("course");
        let content_path = content_path.to_str().unwrap();
        let path = format!("{}/{}/{}/{}.{}", content_path,course_slug , chapter_name_join , section_name_join , ext);
        path
}

#[get("/api/web/section/student/session")]
pub async fn get_section_student_session(
    session: Session,
    http_request: HttpRequest,
    app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {

    let start_time = Utc::now();

    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }

    let user_id = user_id.unwrap();

    let conn = &app_state.conn;
    let now = Local::now().naive_local();

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 读取账号是否存在
    let user_record = match Query::user_find_by_id(&txn, user_id).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
        }
    };

    // 用户不存在
    if user_record.is_none() {
        txn.rollback().await.unwrap();
        return Err(BusinessError::AccountError { reason: "用户不存在或尚未同步".to_string() });
    }

    let user_record = user_record.unwrap();

    let enable_correction_mode_config = match Query::system_config_find_by_key(&txn, "enable_correction_mode").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    let enable_correction_mode_config = enable_correction_mode_config.unwrap_or("0".to_string());

    // 自系统配置表读取当前课程
    let current_train_plan_id = match Query::system_config_find_by_key(&txn, "current_train_plan_id").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前开放课程失败 {}", e.to_string()).to_string() });
        }
    };

    let current_train_plan_id = current_train_plan_id.unwrap_or("0".to_string());
    
    let mut response: HashMap<String, Value> = HashMap::new();

    // 读取学生端界面风格配置
    let enable_modern_style_config = match Query::system_config_find_by_key(&txn, "enable_modern_style").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    let mut user_response: serde_json::Map<String, Value> = serde_json::Map::new();
    user_response.insert(String::from("id"), Value::Number(user_record.id.into()));
    user_response.insert(String::from("username"),  Value::String(user_record.username.clone()));
    user_response.insert(String::from("display_name"),  Value::String(user_record.name.clone()));

    if user_record.avatar.is_none() {
        user_response.insert(String::from("avatar"),  Value::Null);
    }
    else {
        user_response.insert(String::from("avatar"),  Value::String(user_record.avatar.unwrap()));
    }

    response.insert(String::from("user"),  Value::Object(user_response));
    response.insert(String::from("server_timestamp"), Value::Number(((now.and_utc().timestamp()  - 8 * 3600) * 1000).into()));
    response.insert(String::from("enable_modern_style"),  Value::String(enable_modern_style_config.unwrap_or("0".to_string())));
    response.insert(String::from("enable_correction_mode"),  Value::String(enable_correction_mode_config));
    response.insert(String::from("current_train_plan_id"),  Value::String(current_train_plan_id));

    // 记录日志
    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/train/student/session", user_record.id.clone(), start_time);

    BusinessResponse::ok(response).to_json_result()
}

// 微应用提交记录
#[derive(Deserialize)]
struct CommitSourceParams{
  pub uuid: String,

  pub school_slug: String,
  pub course_slug: String,


  pub section_id: i32,

  pub code: Vec<String>,

  pub chapter_name: String,
  pub section_name: String,
  
  pub total_score: i32,
  pub code_count: i32,
  pub pass_count: i32,

  pub compile_status: i32,
  
}
#[post("/api/web/course/section/microapps/submit")]
pub async fn commit_source_block_record(
  session: Session,
  params : web::Json<CommitSourceParams>,
  http_request: HttpRequest,
  app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>{
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();
    let conn = &app_state.conn;
    let uuid = params.uuid.clone();
    let start_time = Utc::now();
    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    // 去数据库中查找记录,存在修改，不存在创建
    let progress_record = match Query::get_progress_by_user(&txn, user_id,params.section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前下载次数失败 {}", e.to_string()).to_string() });
        }
    };
    let mut response: HashMap<String, String> = HashMap::new();
    let code_json: String = params.code.join("");
    match params.compile_status {
        0 => {
            // 未编译
            
            response.insert("school_slug".to_string(), params.school_slug.to_string());
            response.insert("course_slug".to_string(), params.course_slug.to_string());
            response.insert("chapter_name".to_string(), params.chapter_name.to_string());
            response.insert("section_name".to_string(), params.section_name.to_string());
            response.insert("type".to_string(), "notRun".to_string());
            response.insert("status".to_string(), "未运行".to_string());
            response.insert("code".to_string(), code_json);
            response.insert("codeCount".to_string(), params.code_count.to_string());
        },
        1 => {
            // 编译通过
            response.insert("school_slug".to_string(), params.school_slug.to_string());
            response.insert("course_slug".to_string(), params.course_slug.to_string());
            response.insert("chapter_name".to_string(), params.chapter_name.to_string());
            response.insert("section_name".to_string(), params.section_name.to_string());
            response.insert("type".to_string(), "success".to_string());
            response.insert("status".to_string(), "运行通过".to_string());
            response.insert("code".to_string(), code_json);
            response.insert("codeCount".to_string(), params.code_count.to_string());
        },
        2 => {
            // 编译未通过
            response.insert("school_slug".to_string(), params.school_slug.to_string());
            response.insert("course_slug".to_string(), params.course_slug.to_string());
            response.insert("chapter_name".to_string(), params.chapter_name.to_string());
            response.insert("section_name".to_string(), params.section_name.to_string());
            response.insert("type".to_string(), "error".to_string());
            response.insert("status".to_string(), "运行报错".to_string());
            response.insert("code".to_string(), code_json);
            response.insert("codeCount".to_string(), params.code_count.to_string());
        },
        _ => {
            // 非法状态码
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("编译状态错误").to_string() });
        }
    }
    // 项目书部分暂时不做处理
    // 解析原始 JSON 数据
    let mut record_map: HashMap<String, HashMap<String, String>> = HashMap::new();
    
    if let Some(ref record) = progress_record {
        if let Some(record_value) = record.get("record") {
            // 使用 record_value
            record_map = serde_json::from_value(record_value.clone()).expect("Failed to parse JSON string");
        }
    }
    
    // 遍历新数据并进行更改或拼接
    record_map.insert(uuid, response.clone());
    // 将reccord_map转换为json格式
    let record_map = serde_json::to_value(&record_map).unwrap();
    // 提交记录
    if progress_record.is_some() {



        // 修改提交记录
        match Mutation::update_progress_by_user(&txn, user_id,params.section_id,&record_map, params.total_score, params.pass_count,start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("更新记录失败 {}", e.to_string()).to_string() });
            }
        };
    }
    else {
        // 创建提交记录
        match Mutation::create_progress_by_user(&txn, user_id,params.section_id,&record_map, params.total_score, params.pass_count,start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("创建记录失败 {}", e.to_string()).to_string() });
            }
        };
    }


    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    // 反馈给教师事件
    let notice_message = r#"{{"type":"update"}}"#.to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
    let websocket_server = &app_state.ws_server;
    let _resp = websocket_server.send(msg).await;

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/course/section/microapps/submit", user_id, start_time);
    
    BusinessResponse::ok(true).to_json_result()
}
// 提交代码题答题记录
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
struct Rule{
    course_slug: String,
    chapter_name: String,
    section_name: String,
    section_id: i32,
    uuid: String,
    is_runned: bool,
    kernel: serde_json::Value,
}
#[post("/api/web/course/section/ai/commit")]
pub async fn commit_ai_record(
    session: Session,
    params : web::Json<Rule>,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
  ) -> Result<HttpResponse, BusinessError>{
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();

    let uuid = params.uuid.clone();
    // 获取课程文件
    let content_path = get_path(params.course_slug.clone(), params.chapter_name.clone(), params.section_name.clone());
    let file_content = fs::read_to_string(Path::new(&content_path)).expect("加载课程目录文件失败");
    let course_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");

    let course_content = course_content["cells"].as_array().unwrap();
    let mut uuid_list = Vec::new();
    let mut cell_type: Option<String> = None;
    for item in course_content.iter() { 
        if item["cell_type"] != "code" || item["metadata"]["type" ] == "resources" || item["metadata"]["type" ] == "filepreviewer" {
            continue;
        } 
        println!("{}", item);
        let cur_uuid = item["metadata"]["UUID"].as_str().unwrap();
        if cur_uuid == uuid {
            cell_type = match item["metadata"]["type" ].as_str() {
                Some(r) => Some(r.to_string()),
                _ => None,  
            }
        }
        uuid_list.push(uuid.to_string());
    }
    println!("{:?}", uuid_list);
    let total_score = uuid_list.len() as i32;

    let conn = &app_state.conn;
    let start_time = Utc::now();
    // 记录状态
    let mut _type = String::new();
    let mut _status = String::new();
    if params.is_runned {
        if &params.kernel["code_result"][0]["type"] == "error" {
            _type = "error".to_string();
            _status = "运行报错".to_string();
        } else {
            _type = "success".to_string();
            _status = "运行通过".to_string();
        }
        
    }else {
        _type = "notRun".to_string();
        _status = "未运行".to_string();
    }
    let code_result = params.kernel.get("codeResult")
            .and_then(Value::as_array)
            .unwrap();
    let code_json = json!(
        {
            "type": _type,
            "status": _status,
            "code": params.kernel["code"],
            "result": code_result,
        }
    );

    let mut record = HashMap::new();
    
    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    
    // 去数据库中查找记录,存在修改，不存在创建
    let progress_record = match Query::get_progress_by_user(&txn, user_id,params.section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前下载次数失败 {}", e.to_string()).to_string() });
        }
    };
    // 提交记录
    if let Some(progress_record) = progress_record {
        // 获取总分和正确数
        let mut pass_count = progress_record["pass_count"].as_i64().unwrap() as i32;
        // let mut record_len = progress_record["total_score"].as_i64().unwrap() as i32;
        
        // 对比记录
        let mut history_progress_record: HashMap<String,Value> = serde_json::from_value(progress_record["record"].clone()).unwrap();
        // 查找对应uuid的内容
        let mut can_search = false;
        let json_value: Value = serde_json::from_value(progress_record["record"].clone()).expect("Failed to parse JSON");

        // 提取 type 字段的值
        if let Some(inner_data) = json_value.get(uuid.clone()) {
            can_search = true;
            if let Some(type_value) = inner_data.get("type") {
                if let Some(type_str) = type_value.as_str() {
                    if type_str != "success" && _type == "success" {
                        pass_count += 1 ;
                    }else if type_str == "success" && _type != "success" {
                        pass_count -= 1 ;
                    }
                    
                }
            }
        }
        if !can_search {
            println!("{}", uuid);
            if _type == "success" || cell_type.is_some(){
                pass_count += 1 ;
            }
        }
        history_progress_record.insert(uuid, code_json);
        
        let record_json = serde_json::to_value(history_progress_record).unwrap();

        // 修改提交记录
        match Mutation::update_progress_by_user(&txn, user_id,params.section_id,&record_json, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("更新记录失败 {}", e.to_string()).to_string() });
            }
        };
    }
    else {
        // let record_len = 1;
        // let record_correct_len = 1;
        let mut pass_count = 0;
        if _type == "success" || cell_type.is_some(){
            pass_count = 1 ;
        }
        record.insert(uuid, code_json);
        let record_json = serde_json::to_value(record).unwrap();
        // 创建提交记录
        match Mutation::create_progress_by_user(&txn, user_id,params.section_id,&record_json, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("创建记录失败 {}", e.to_string()).to_string() });
            }
        };
    }
        // 结束事务
        match txn.commit().await {
            Ok(r) => r,
            Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        };
        // 反馈给教师事件
        let notice_message = r#"{{"type":"update"}}"#.to_string();
        let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
        let websocket_server = &app_state.ws_server;
        let _resp = websocket_server.send(msg).await;
    
        let connection_info = http_request.connection_info();
        let client_ip = connection_info.peer_addr().unwrap();
        log_request(client_ip, "/api/web/course/section/ai/commit", user_id, start_time);
        
        BusinessResponse::ok(true).to_json_result()
  }

// 提交客观题答题记录 
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
struct CommitOiParams{
    pub chapter_name: String,
    pub course_slug: String,
    pub questions: Value,
    pub section_name: String,
    pub section_id: i32,
}
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
struct JudgeResult{
    pub answer: String,
    pub raw_answer: String,
    pub status: bool
}
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
struct JudgeResultValue{
    pub answer: Value,
    pub raw_answer: Value,
    pub status: Option<bool>
}
#[post("/api/web/course/section/oi/submit")]
pub async fn commit_oi_record(
  session: Session,
  params : web::Json<CommitOiParams>,
  http_request: HttpRequest,
  app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>{
    let conn = &app_state.conn;
    let start_time = Utc::now();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();

    // 获取xml文件
    let answer_path = get_path(params.course_slug.clone(), params.chapter_name.clone(), params.section_name.clone());
    // let file = File::open(answer_path).expect("Could not open file");
    let file_content = fs::read_to_string(answer_path).expect("加载课程目录文件失败");
    // println!("file:{:?}", file_content);
    let file_content:Value = serde_json::from_str(&file_content).unwrap();
    // println!("{:?}", file_content);
    let answers: Vec<service::Question> = serde_json::from_value(file_content["questions"].clone()).unwrap();

    let mut record: HashMap<String, JudgeResultValue> = HashMap::new();
    let mut record_res: HashMap<String, Value> = HashMap::new();

    let questions: Value = params.questions.clone();

    if let Some(questions) = questions.as_object() {
    // println!("questions:{:?}", questions);

        for (uuid, question) in questions {
            let answer = question.clone()["answer"].clone();
            // 获取目标答案
            let mut raw_answer = String::new();
            // 获取题目类型
            let mut course_type = String::new();
            for question_data in &answers {
                if question_data.UUID != uuid.clone(){
                    continue;
                }
                if let Some(answer_data) = &question_data.answer {
                    raw_answer = answer_data.clone().to_string();
                    course_type = question_data.questionType.clone();
                    break;
                }
        
            }
            
            match course_type.as_str() {
                "单选题" => {
                    let answer = answer.as_str();
                    println!("answer:{:?}", answer);
                    // 如果answer为null,或者是空字符串，则不设置status字段
                    if answer == None || answer.unwrap().is_empty() {
                        record.insert(uuid.to_string(), JudgeResultValue { 
                            answer: serde_json::Value::Null, 
                            raw_answer: serde_json::Value::String(raw_answer.replace("\"", "")), 
                            status: None 
                        });
                        continue;
                    }
                    let answer = answer.unwrap().to_string();
                    raw_answer = raw_answer.replace("\"", "");
                    if answer == raw_answer {
                        record.insert(uuid.to_string(), JudgeResultValue { answer: serde_json::Value::String(answer.to_string()), raw_answer: serde_json::Value::String(raw_answer.to_string()), status: Some(true) });
                    } else {
                        record.insert(uuid.to_string(), JudgeResultValue { answer: serde_json::Value::String(answer.to_string()), raw_answer: serde_json::Value::String(raw_answer.to_string()), status: Some(false) });
                    }
                },
                "多选题" => {
                    println!("answer:{:?}", answer);
                    // 检查答案是否为空
                    if answer.is_null() || (answer.is_array() && answer.as_array().unwrap().is_empty()) {
                        let parsed_raw_answer: Value = serde_json::from_str(&format!("[\"{}\", \"{}\"]", "B", "C")).unwrap();
                        record.insert(uuid.to_string(), JudgeResultValue { 
                            answer, 
                            raw_answer: parsed_raw_answer, 
                            status: None 
                        });
                        continue;
                    }
                    
                    let parsed_raw_answer: Value = serde_json::from_str(&format!("[\"{}\", \"{}\"]", "B", "C")).unwrap();
                    raw_answer = raw_answer.replace("\"", "");

                    // 比较两个 Value
                    if parsed_raw_answer == answer {
                        record.insert(uuid.to_string(), JudgeResultValue { answer, raw_answer: parsed_raw_answer, status: Some(true) });
                    } else {
                        record.insert(uuid.to_string(), JudgeResultValue { answer, raw_answer: parsed_raw_answer, status: Some(false) });
                    }
                    
                },
                "填空题" => {
                    println!("answer:{:?}", answer);
                    // 解析 raw_answer
                    let parsed_raw_answer: Value = serde_json::from_str(&raw_answer).unwrap();

                    // 提取 answer 的 text 值
                    let answer_texts = if let Value::Array(arr) = &answer {
                        arr.iter()
                            .filter_map(|item| {
                                if let Value::Object(obj) = item {
                                    if let Some(Value::Array(texts)) = obj.get("text") {
                                        return Some(texts.iter().filter_map(|t| t.as_str()).collect::<Vec<&str>>());
                                    }
                                }
                                None
                            })
                            .flatten()
                            .collect::<Vec<&str>>()
                    } else {
                        Vec::new()
                    };

                    // 检查答案是否为空
                    if answer.is_null() || answer_texts.is_empty() {
                        record.insert(uuid.to_string(), JudgeResultValue { 
                            answer, 
                            raw_answer: parsed_raw_answer, 
                            status: None 
                        });
                        continue;
                    }

                    // 提取 parsed_raw_answer 的 text 值
                    let raw_answer_texts = if let Value::Array(arr) = &parsed_raw_answer {
                        arr.iter()
                            .filter_map(|item| {
                                if let Value::Object(obj) = item {
                                    if let Some(Value::Array(texts)) = obj.get("text") {
                                        return Some(texts.iter().filter_map(|t| t.as_str()).collect::<Vec<&str>>());
                                    }
                                }
                                None
                            })
                            .flatten()
                            .collect::<Vec<&str>>()
                    } else {
                        Vec::new()
                    };

                    // 比较 text 值
                    let is_match = answer_texts.iter().any(|text| raw_answer_texts.contains(text));
                    match is_match {
                        true => {
                            record.insert(uuid.to_string(), JudgeResultValue { answer, raw_answer: parsed_raw_answer, status: Some(true) });
                        },
                        false => {
                            record.insert(uuid.to_string(), JudgeResultValue { answer, raw_answer: parsed_raw_answer, status: Some(false) });
                        }
                    }
                },
                "选择填空题" => {
                    let raw_answer: Value = serde_json::from_str(&raw_answer).expect("Failed to parse raw_answer");
                    println!("answer:{:?}", answer);
                    // 检查答案是否为空
                    if answer.is_null() ||        
                        (answer.is_string() && answer.as_str().unwrap().is_empty()) ||
                        (answer.is_object() && answer.as_object().unwrap().is_empty()) {
                            record.insert(uuid.to_string(), JudgeResultValue { 
                                answer, 
                                raw_answer, 
                                status: None 
                            });
                            continue;
                        }

                    if answer == raw_answer {
                        record.insert(uuid.to_string(), JudgeResultValue { answer, raw_answer, status: Some(true) });
                    } else {
                        record.insert(uuid.to_string(), JudgeResultValue { answer, raw_answer, status: Some(false) });
                    }
                },
                _ => {}
            }           
        }
    }
    // 获取答题总数
    let record_len: i32 = record.len().try_into().unwrap();
    // 获取其中正确的答题数（只统计有status且为true的）
    let record_correct_len: i32 = record.values().filter(|x| x.status == Some(true)).count().try_into().unwrap(); 

    record_res = record
        .into_iter()
        .map(|(k, v)| {
            let mut json_obj = serde_json::Map::new();
            json_obj.insert("answer".to_string(), v.answer);
            json_obj.insert("rawAnswer".to_string(), v.raw_answer);
            
            // 只有当status存在时才包含它
            if let Some(status) = v.status {
                json_obj.insert("status".to_string(), serde_json::Value::Bool(status));
            }
            
            (k, serde_json::Value::Object(json_obj))
        })
        .collect();
    


    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 去数据库中查找记录,存在修改，不存在创建
    let progress_record = match Query::get_progress_by_user(&txn, user_id,params.section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前下载次数失败 {}", e.to_string()).to_string() });
        }
    };

    // 提交记录
    if progress_record.is_some() {
        let mut submit_times = 1;
        if let Some(progress_record) = progress_record {
            let records = &progress_record["record"];
            let progress_record_map:HashMap<String, Value> = serde_json::from_value(records.clone()).unwrap();

            // let submit_times = progress_record_map.get("submitTimes").unwrap();


            // submit_times = submit_times.as_i64().unwrap() as i32;
            let history_submit_times = if let Some(value) = progress_record_map.get("submitTimes") {
                if let Some(i64_value) = value.as_i64() {
                    i64_value as i32
                } else {
                    // 处理 as_i64() 返回 None 的情况
                    eprintln!("Error: 'submitTimes' is not a valid i64");
                    1 // 默认值
                }
            } else {
                // 处理 get("submitTimes") 返回 None 的情况
                eprintln!("Error: 'submitTimes' key not found");
                1 // 默认值
            };
            submit_times = history_submit_times;
        } 
        submit_times += 1;
        record_res.insert("submitTimes".to_string(), json!(submit_times));

        // 将记录转换为json格式,且避免转义发生
        let record_json = serde_json::to_value(record_res.clone()).unwrap();
        // 修改提交记录
        match Mutation::update_progress_by_user(&txn, user_id,params.section_id,&record_json, record_len, record_correct_len, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("更新记录失败 {}", e.to_string()).to_string() });
            }
        };
    }
    else {
        record_res.insert("submitTimes".to_string(), json!(1));
        // 将记录转换为json格式,且避免转义发生
        let record_json = serde_json::to_value(record_res.clone()).unwrap();
        // 创建提交记录
        match Mutation::create_progress_by_user(&txn, user_id,params.section_id,&record_json, record_len, record_correct_len, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("创建记录失败 {}", e.to_string()).to_string() });
            }
        };
    }

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    // 反馈给教师事件
    let notice_message = r#"{{"type":"update"}}"#.to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
    let websocket_server = &app_state.ws_server;
    let _resp = websocket_server.send(msg).await;

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/course/section/oi/submit", user_id, start_time);

    let res = json!({
        "judgeResult": record_res,
        "totalScore": record_len,
        "passCount": record_correct_len,
    });

    BusinessResponse::ok(res).to_json_result()
}  
// 提交 scratch 记录
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct CommitScratchParams{
    pub chapter_name: String, 
    pub course_slug: String,
    pub section_name: String,
    pub section_id: Option<i32>,
    pub user_i_d: Option<i32>,
    pub student_record: Vec<StudentRecord>,
}
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]

pub struct StudentRecord {
    message: Value,
    file_name: String,
    r#type: String,
}
// #[derive(Deserialize,Serialize,Debug,Clone)]
// #[serde(rename_all = "camelCase")]
// pub struct Message {
//     extensions: Option<Vec<String>>,
//     judge_steps: Vec<JudgeStep>,
//     meta: Value,
//     monitors: Vec<String>,
//     targets: Vec<Value>,
// }

#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
struct JudgeStep {
    score: i32,
    pass: bool,
    manual: bool,
    rate: i32,
    code: String,
}

#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
struct Costumes {
    pub asset_id: String,
    pub name: String,
    pub md5ext: String,
    pub data_format: String,
    pub rotation_center_x: i32,
    pub rotation_center_y: i32,
}
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
struct Blocks {
    pub opcode: String,
    pub next: String,
    pub parent: String,
    pub inputs: HashMap<String, Vec<String>>, // TODO: 待实现
    pub fields: HashMap<String, Vec<String>>,
    pub shadow: bool,
    pub top_level: bool,
}

#[post("/api/web/course/section/scratch/submit")]
pub async fn commit_scratch_record(
    session: Session,
    params : web::Json<CommitScratchParams>,
    // request: web::Bytes,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>{
    let conn = &app_state.conn;
    let start_time = Utc::now();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let mut user_id = user_id.unwrap();
    if params.user_i_d.is_some() { 
        user_id = params.user_i_d.unwrap();   
    }
    // println!("user_id:{}", user_id);
    // let path_dir = get_path_in_exe_dir("course");
    // let full_name = format!("{}/{}.json", params.chapter_name, params.section_name);
    let full_name = format!("{}.json", params.section_name);

    // let target_dir = format!("{:?}/student/{}/{}/{}", path_dir, params.course_slug.clone(), user_id, full_name.clone());

    let new_student_record ;

    let student_file = &params.student_record[0];
    // let judge_steps = &student_file.message.judge_steps;
    new_student_record = CodeResult {
            file_name: student_file.file_name.clone(),
            r#type: student_file.r#type.clone(),
            message: student_file.message.clone(),
        };
      
    match post_scratch_file(&user_id, &params.course_slug, &new_student_record, &params.chapter_name).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("生成文件失败 {}", e.to_string()) })
    };
    
    let mut pass_count = 0;
    let mut total_score = 0;
    // let mut student_steps = Vec::new();
    // let judge_steps = &params.student_record[0].message.judge_steps;    
    let judge_steps:Vec<Value> = serde_json::from_value(student_file.message["judgeSteps"].clone()).unwrap_or(
        vec![]
    );

    // println!("judge_steps: {:?}", judge_steps);
    if params.student_record[0].r#type == "MicroBit" {
        pass_count = 1;
        total_score = 1;
    } else if params.student_record[0].r#type == "Scratch" {
        total_score = judge_steps.len() as i32;
        for step in judge_steps.clone(){
            let manual = step["manual"].as_bool().unwrap_or(false);
            if manual && step["score"] == step["rate"]{
                pass_count += 1;
            } else if !manual{ 
                let pass = step["pass"].as_bool().unwrap_or(false);
                if pass{
                    pass_count += 1;
                }
            }
        }
    }

    let progress = json! ({
        "file_name": full_name.to_string(),
        "time": Utc::now().format("%Y-%m-%d %H:%M:%S").to_string(),
        "judge_steps": judge_steps,
    });
    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    let section_id;
    if params.section_id.is_none() { 
        println!("course_slug: {} chapter_name: {} section_name: {}", params.course_slug, params.chapter_name, params.section_name);
        // 获取section_id
        section_id = match Query::get_section_id(&txn, params.course_slug.clone(), params.chapter_name.clone(), params.section_name.clone()).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::ProcessError { reason: format!("获取sectionID失败 {}", e.to_string()).to_string() });
            }
        };
    } else {
        section_id = params.section_id.unwrap();
    }


    // 去数据库中查找记录,存在修改，不存在创建
    let progress_record = match Query::get_progress_by_user(&txn, user_id, section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("自系统配置内获取当前下载次数失败 {}", e.to_string()).to_string() });
        }
    };

    // let new_student_record = serde_json::to_value(new_student_record).unwrap();
    // 提交记录
    if progress_record.is_some() {
        // 修改提交记录
        match Mutation::update_progress_by_user(&txn, user_id, section_id,&progress, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::ProcessError { reason: format!("更新记录失败 {}", e.to_string()).to_string() });
            }
        };
    }
    else {
        // 创建提交记录
        match Mutation::create_progress_by_user(&txn, user_id, section_id,&progress, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::ProcessError { reason: format!("创建记录失败 {}", e.to_string()).to_string() });
            }
        };
    }

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    // 反馈给教师事件
    let notice_message = r#"{{"type":"update"}}"#.to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
    let websocket_server = &app_state.ws_server;
    let _resp = websocket_server.send(msg).await;

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/course/section/scratch/submit", user_id, start_time);

    BusinessResponse::ok(1).to_json_result()
}  
#[post("/api/web/course/section/scratch/save")]
pub async fn save_scratch_record(
    session: Session,
    params : web::Json<CommitScratchParams>,
    // request: web::Bytes,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>{
    let start_time = Utc::now();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();


    let new_student_record ;

    let student_file = &params.student_record[0];
    // let judge_steps = &student_file.message.judge_steps;
    new_student_record = CodeResult {
            file_name: student_file.file_name.clone(),
            r#type: student_file.r#type.clone(),
            message: student_file.message.clone(),
        };
      
    match post_scratch_file(&user_id, &params.course_slug, &new_student_record, &params.chapter_name).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("生成文件失败 {}", e.to_string()) })
    };

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/course/section/scratch/submit", user_id, start_time);

    BusinessResponse::ok(1).to_json_result()
}  
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct ClearStudentRecord{
    pub chapter_name: String, 
    pub course_slug: String,
    pub section_name: String,
    // pub section_id: i32,
}
#[post("/api/web/section/clearStudentRecord")]
pub async fn clear_student_record(
    session: Session,
    params : web::Json<ClearStudentRecord>,
) -> Result<HttpResponse, BusinessError>{
    
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();
    let (chapter, course_slug, section) = (params.chapter_name.clone(), params.course_slug.clone(), params.section_name.clone());
    let full_name = format!("{}.json", section);
    let file_path = get_path_in_exe_dir("static").join("student").join("course").join(course_slug.clone()).join(user_id.to_string()).join(chapter.clone()).join(full_name.clone());
    if file_path.exists() {
        // 删除文件
        match std::fs::remove_file(file_path) {
            Ok(r) => r,
            Err(e) => return Err(BusinessError::InternalError { reason: format!("无法删除文件 {}", e.to_string()).to_string() })
        };
    }
    BusinessResponse::ok(1).to_json_result()
}
// 操作题提交
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct CommitOperationParams{
    pub chapter_name: String, 
    pub course_slug: String,
    pub section_name: String,
    pub section_id: i32,
    pub student_answer: Value,
}
#[post("/api/web/course/section/operation/submit")]
pub async fn commit_operation_record(
    session: Session,
    params : web::Json<CommitOperationParams>,
    // request: web::Bytes,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>{
    let conn = &app_state.conn;
    let start_time = Utc::now();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();
    

    let progress = params.student_answer.clone();
    let steps: Result<Vec<Value>, serde_json::Error> = match progress["steps"].as_array() {
        Some(arr) => Ok(arr.clone()),
        None => return  Err(BusinessError::ProcessError { reason: "无法解析步骤".to_string() }),
    };
    let steps = match steps {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法解析步骤 {}", e.to_string()).to_string() })
    };
    let mut pass_count = 0;
    let total_score = steps.len() as i32;
    for step in steps {
        let step_score = step["status"].as_str().unwrap();
        if step_score == "finish" {
            pass_count += 1;
        }
    }
    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    
    // 去数据库中查找记录,存在修改，不存在创建
    let progress_record = match Query::get_progress_by_user(&txn, user_id,params.section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前下载次数失败 {}", e.to_string()).to_string() });
        }
    };

    // let new_student_record = serde_json::to_value(new_student_record).unwrap();
    // 提交记录
    if progress_record.is_some() {
        // 修改提交记录
        match Mutation::update_progress_by_user(&txn, user_id,params.section_id,&progress, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("更新记录失败 {}", e.to_string()).to_string() });
            }
        };
    }
    else {
        // 创建提交记录
        match Mutation::create_progress_by_user(&txn, user_id,params.section_id,&progress, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("创建记录失败 {}", e.to_string()).to_string() });
            }
        };
    }

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    // 反馈给教师事件
    let notice_message = r#"{{"type":"update"}}"#.to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
    let websocket_server = &app_state.ws_server;
    let _resp = websocket_server.send(msg).await;

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/course/section/operation/submit", user_id, start_time);

    BusinessResponse::ok(1).to_json_result()
}
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct CommitMicroAppParams{
    pub uuid: String, 
    pub chapter_name: String,
    pub code_result: Vec<MicroAppCodeResult>,
    pub course_slug: String,
    pub section_name: String,
    pub section_id: i32,
}

#[post("/api/web/course/section/microapp/submit")]
pub async fn commit_microapp_record(
    session: Session,
    params : web::Json<CommitMicroAppParams>,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>{
    let conn = &app_state.conn;
    let start_time = Utc::now();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();
    let code_result = params.code_result.clone();
    let new_student_record = code_result[0].clone();
    let file_type = new_student_record.r#type.clone();
    let uuid = params.uuid.clone();

    // 获取课程文件
    let content_path = get_path(params.course_slug.clone(), params.chapter_name.clone(), params.section_name.clone());
    let file_content = fs::read_to_string(Path::new(&content_path)).expect("加载课程目录文件失败");
    let course_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");

    let course_content = course_content["cells"].as_array().unwrap();
    let mut uuid_list = Vec::new();
    let mut cell_type: Option<String> = None;
    for item in course_content.iter() { 
        if item["cell_type"] != "code" || item["metadata"]["type" ] == "resources" || item["metadata"]["type" ] == "filepreviewer" {
            continue;
        } 
        println!("{}", item);
        let cur_uuid = item["metadata"]["UUID"].as_str().unwrap();
        if cur_uuid == uuid {
            cell_type = match item["metadata"]["type" ].as_str() {
                Some(r) => Some(r.to_string()),
                _ => None,  
            }
        }
        uuid_list.push(uuid.to_string());
    }
    println!("{:?}", uuid_list);
    let total_score = uuid_list.len() as i32;

    let file_name = new_student_record.file_name.clone();
    let mut record = HashMap::new();

    
    match post_micro_app_file(&user_id, &params.course_slug, &new_student_record, &params.chapter_name).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("生成文件失败 {}", e.to_string()) })
    };
    // println!("file_name: {}", file_name);
    let full_name = match file_type.as_str() {
        "text" => format!("{}.txt", file_name),
        "table" => format!("{}.csv", file_name),
        "mind" | "flow" | "networksimulator" | "spreadsheet" | "drawio" => format!("{}.json", file_name),
        _ => file_name.to_string(), // 默认情况下返回原始文件名
    };

    let microapp_json = json!({
        "fileName":full_name,
        "time": Utc::now().format("%Y-%m-%d %H:%M:%S").to_string(),
        "microAppFileType":file_type,
    });
    
    

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    
    // 去数据库中查找记录,存在修改，不存在创建
    let progress_record = match Query::get_progress_by_user(&txn, user_id,params.section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前下载次数失败 {}", e.to_string()).to_string() });
        }
    };


   // 提交记录
   if let Some(progress_record) = progress_record {
        // 获取总分和正确数
        // let mut record_correct_len = progress_record["pass_count"].as_i64().unwrap() as i32;
        let mut pass_count = progress_record["pass_count"].as_i64().unwrap() as i32;
        // 对比记录
        let mut history_progress_record: HashMap<String,Value> = serde_json::from_value(progress_record["record"].clone()).unwrap();
        // 查找对应uuid的内容
        let mut can_search = false;
        let json_value: Value = serde_json::from_value(progress_record["record"].clone()).expect("Failed to parse JSON");
        if let Some(inner_data) = json_value.get(uuid.clone()) {
            can_search = true;
        }
        if !can_search {
            // println!("{}", uuid);
            pass_count += 1;

        }

        history_progress_record.insert(uuid, microapp_json);
        
        let record_json = serde_json::to_value(history_progress_record).unwrap();

        // 修改提交记录
        match Mutation::update_progress_by_user(&txn, user_id,params.section_id,&record_json, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("更新记录失败 {}", e.to_string()).to_string() });
            }
        };
    }
    else {
        let pass_count = 1;
        record.insert(uuid, microapp_json);
        let record_json = serde_json::to_value(record).unwrap();
        // println!("{:?}",record_json);
        // 创建提交记录
        match Mutation::create_progress_by_user(&txn, user_id,params.section_id,&record_json, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("创建记录失败 {}", e.to_string()).to_string() });
            }
        };
    }

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    // 反馈给教师事件
    let notice_message = r#"{{"type":"update"}}"#.to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
    let websocket_server = &app_state.ws_server;
    let _resp = websocket_server.send(msg).await;

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/course/section/microapp/submit", user_id, start_time);

    BusinessResponse::ok(1).to_json_result()
}
// 编程填空题提交
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct CommitCodeAnswerParams{

    pub chapter_name: String,
    pub course_slug: String,
    pub section_name: String,
    pub student_answer: HashMap<String,String>,
    pub section_id: i32,
}

#[post("/api/web/course/section/codeAnswer/submit")]
pub async fn commit_code_anwser_record(
    session: Session,
    params : web::Json<CommitCodeAnswerParams>,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>{
    let conn = &app_state.conn;
    let start_time = Utc::now();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();
    
    let student_answer = params.student_answer.clone();
    let slug = params.course_slug.clone();
    let chapter = params.chapter_name.clone();
    let section = params.section_name.clone();

    // 获取对应course文件路径
    let course_path = get_path_in_exe_dir("course");
    let course_path = course_path.join(slug.clone()).join("course.json");
    
    // 解析目录文件，拼接对应字段
    let mut chapter_name = String::new();
    let mut section_name = String::new();
    
    let file_content = fs::read_to_string(course_path).expect("加载课程目录文件失败");
    let course_content: Value = serde_json::from_str(&file_content).unwrap();
    let mut ext = String::new();

    

    //遍历course_content中的indics字段，获取必要信息
    for value in course_content["indics"].as_array().unwrap() {
        // 获取chapter_name
        if value["chapterName"] == chapter {
            chapter_name = format!("{}.{}", value["chapterIndex"],value["chapterName"]).replace("\"", "");
            // 获取section_name
            for section_value in value["sections"].as_array().unwrap() {
                if section_value["sectionName"] == section {
                    section_name = format!("{}.{}", section_value["sectionIndex"],section_value["sectionName"]).replace("\"", "");
                    ext = section_value["ext"].as_str().unwrap().to_string();
                }
            }
        }
    }
    let mut progress = HashMap::new();
    // 获取课程内容文件
    let content_path = get_path_in_exe_dir("course");
    let content_path = content_path.to_str().unwrap();
    let path = format!("{}/{}/{}/{}.{}", content_path,slug , chapter_name , section_name , ext);
    
    let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");
    let file_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
    let mut pass_count = 0;
    // 获取 questions 数据
    if let Some(questions) = file_content.get("questions").and_then(|q| q.as_array()) {
        for question in questions {
            if let Some(answer) = question.get("answer").and_then(|a| a.as_object()) {
                for (blank, answers) in answer {
                    if let Some(answers) = answers.as_array() {
                        for answer in answers {
                            if let Some(answer_str) = answer.as_str() {
                                if student_answer.get(blank) == Some(&answer_str.to_string()) {
                                    let code_current_record = json!({
                                        "status": true, 
                                        "studentAnswer": student_answer.get(blank),
                                    });
                                    progress.insert(blank.to_string(), code_current_record);
                                    pass_count += 1;
                                } else {
                                    let code_current_record = json!({
                                        "status": false, 
                                        "studentAnswer": student_answer.get(blank),
                                    });
                                    progress.insert(blank.to_string(), code_current_record);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    let total_score = progress.len() as i32;
    let mut result =json!({
        "judgeResult":progress,
        "totalScore": total_score,
        "passCount": pass_count,
        "submitTimes": 1,
    });
    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    
    // 去数据库中查找记录,存在修改，不存在创建
    let progress_record = match Query::get_progress_by_user(&txn, user_id,params.section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前下载次数失败 {}", e.to_string()).to_string() });
        }
    };
    let mut submit_time = 1;
    if progress_record.is_some(){
        if let Some(submit_times) = progress_record.clone().unwrap()["record"].get("submitTimes").and_then(|s| s.as_i64()) {
            submit_time += submit_times;
        }
    }
    progress.insert("submitTimes".to_string(), json!(submit_time));
    result["submitTimes"] = submit_time.into();
    let progress = serde_json::to_value(progress).unwrap();
    // 提交记录
    if progress_record.is_some() {
        
        // 修改提交记录
        match Mutation::update_progress_by_user(&txn, user_id,params.section_id,&progress, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("更新记录失败 {}", e.to_string()).to_string() });
            }
        };
    }
    else {
        // 创建提交记录
        match Mutation::create_progress_by_user(&txn, user_id,params.section_id,&progress, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::InternalError { reason: format!("创建记录失败 {}", e.to_string()).to_string() });
            }
        };
    }

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    // 反馈给教师事件
    let notice_message = r#"{{"type":"update"}}"#.to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
    let websocket_server = &app_state.ws_server;
    let _resp = websocket_server.send(msg).await;

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/course/section/codeAnswer/submit", user_id, start_time);

    BusinessResponse::ok(result).to_json_result()
}
// 提交 oj 记录
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct CommitOjParams{
    pub source: String,
    pub language: String,
    pub state: State,
    pub solution_type: String,
    pub section_id: i32,
}
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]

pub struct State {
    chapter_name: String,
    course_slug: String,
    section_name: String,
    solution_type: String,
}
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]

pub struct Detail {
    score: i32,
    state: i32,
    user_time_usage: String,
    max_memory_usage: String,
    system_time_usage: String,
}
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct OjRecord {
    count: i32,
    score: i32,
    detail: Vec<Detail>,
    source: String,
    status: i32,
    history:  Vec<Vec<Detail>>,
    message: String,
    language: String,
    inout_data: Value,
    solution_type: String,
}

#[post("/api/web/course/section/oj/submit")]
pub async fn commit_oj_record(  
    session: Session,
    params : web::Json<CommitOjParams>,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>{
    const OJ_AC: &str = "4"; // 程序的输出与标准输出完全一致
    const OJ_PE: &str = "5"; // 程序的输出在不考虑空白的情况下，与标准输出一致
    const OJ_WA: &str = "6"; // 错误的答案
    const OJ_TL: &str = "7"; // 超时
    const OJ_RE: &str = "10"; // 运行错误
    
    const TIMEOUT_DURATION: Duration = Duration::from_secs(30);

    let conn = &app_state.conn;
    let start_time = Utc::now();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();

    let data_dir = get_path_in_exe_dir("data").join(params.state.course_slug.clone()).join(params.state.chapter_name.clone()).join(params.state.section_name.clone());
    let mut oj_result: Value = Value::Null;
    
    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    let mut pass_count = 0;
    let history: Vec<Vec<Detail>> = Vec::new();
    // 寻找pyhton路径
    // 检查系统
    let os_version = match get_system_version(){
        Ok(os_version) => os_version,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("不支持的操作系统版本{} ", e)}),
    };
    // let mut python_exe_path = get_path_in_exe_dir("../ipython").join("python-3.9.22-embed-amd64");
    let mut python_exe_path = get_path_in_exe_dir("../ipython").join("python-3.8.20-embed-amd64");
    // if os_version == "win7"{
    //     python_exe_path = get_path_in_exe_dir("../ipython").join("python-3.8.20-embed-amd64");
    // }
    // 如果该路径存在
    // if Path::new(&python_exe_path.join("win10")).exists() {
        
    //     python_exe_path = python_exe_path.join("win10").join("python-3.8.10-embed-amd64").join("python.exe");
    // } else if Path::new(&python_exe_path.join("win11")).exists() {
    //     python_exe_path = python_exe_path.join("win11").join("python-3.8.10-embed-amd64").join("python.exe");
    // }else {
    //     return Err(BusinessError::AccountError { reason: "找不到python环境".to_string() });
    // }
    if python_exe_path.exists() {
        python_exe_path = python_exe_path.join("python.exe");
    } else {
        return Err(BusinessError::AccountError { reason: "找不到python环境".to_string() });
    }
    // 定义 Python 代码
    let code =params.source.clone();

    // 创建正则表达式
    let re = Regex::new(r#"input\((['"][^'"]+['"])\)"#).unwrap();
    // 执行替换
    let code = re.replace_all(&code, "input()").to_string();

    // 创建 .py 文件
    let file_path = data_dir.join("1.py");
    if !file_path.exists() {
        match fs::create_dir_all(&data_dir) {
            Ok(_) => (),
            Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法创建文件夹 {}", e.to_string()).to_string() })
        }
    }
    let mut file = match File::create(&file_path) {
        Ok(f) => f,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法创建文件 {}", e.to_string()).to_string() })
    };
    // 将代码写入文件
    match file.write_all(code.as_bytes()) {
        Ok(_) => (),
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法写入文件 {}", e.to_string())})
    }
    
    // 检查是否有python环境
    let python_check_command = format!("{} --version", python_exe_path.display());
    match Command::new("cmd")
        .args(&["/C", &python_check_command])
        .output()
    {
        Ok(output) => {
            let output_size = output.stdout.len();
            let output_size = output_size.eq(&0);
            if output_size {
                let notice_message = r#"{"type":"python", "message": "未发现可用python环境，请检查教师端是否安装了可用的python，以及环境变量是否设置成功"}"#.to_string();
                let msg: ClientMessage = ClientMessage { id: 0, room: String::from("teacher"), msg: notice_message };
                let websocket_server = &app_state.ws_server;
                let _resp = websocket_server.send(msg).await;  
                return Err(BusinessError::ProcessError {
                    reason: "未发现可用python环境，请反馈教师，检查教师端是否安装了可用的python，以及环境变量是否设置成功".to_string(),
                });
            }
        }
        Err(_) => {
            let notice_message = r#"{"type":"python", "message": "未发现可用python环境，请检查教师端是否安装了可用的python，以及环境变量是否设置成功"}"#.to_string();
            let msg: ClientMessage = ClientMessage { id: 0, room: String::from("teacher"), msg: notice_message };
            let websocket_server = &app_state.ws_server;
            let _resp = websocket_server.send(msg).await;  
            return Err(BusinessError::ProcessError {
                reason: "未发现可用python环境，请反馈教师，检查教师端是否安装了可用的python，以及环境变量是否设置成功".to_string(),
            });
        },
    };

    let mut loop_count = 1;
    

    let python_exe_path_manual = python_exe_path.to_string_lossy();
    let python_exe_path_manual = python_exe_path_manual.replace("\\", "/");
    let file_path_manual = file_path.to_string_lossy();
    let file_path_manual = file_path_manual.replace("\\", "/");
    
    let mut total_score = 0;
    let mut detail_result = Vec::new();
    let mut inout_data = Vec::new();

    loop {

        if !data_dir.join(format!("{}.in" , loop_count)).exists() {
            break;
        }
        // 读取文件内容
        // let input_data = fs::read_to_string(data_dir.join(format!("{}.in" , loop_count))).expect("Something went wrong reading the file");
        let input_path = data_dir.join(format!("{}.in" , loop_count));
        let output_data = fs::read_to_string(data_dir.join(format!("{}.out" , loop_count))).expect("Something went wrong reading the file");
        // 创建接收文件
        let result_path = data_dir.join("1.txt");
        let _ = match File::create(&result_path) {
            Ok(f) => f,
            Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法创建文件 {}", e.to_string()).to_string() })
        };
 
        let input_path_manual = input_path.to_string_lossy();
        let input_path_manual = input_path_manual.replace("\\", "/");
        let result_path_manual = result_path.to_string_lossy();
        let result_path_manual = result_path_manual.replace("\\", "/");

        // 运行命令行并捕获资源使用情况
        let output = match timeout(TIMEOUT_DURATION, async {
            if cfg!(target_os = "windows") {
                // Windows: 使用 PowerShell 捕获资源使用情况
                let powershell_command = format!(
                    r#"
                    try {{
                        # 启动进程并设置重定向
                        $process = Start-Process '{}' -ArgumentList '{}' -NoNewWindow -PassThru -RedirectStandardInput '{}' -RedirectStandardOutput '{}'
                        $processId = $process.Id
                        
                        # 初始化监控变量
                        $peakMemory = 0
                        $privilegedTime = 0
                        $userTime = 0
                        $hasData = $false
                        
                        # 监控循环
                        do {{
                            try {{
                                $currentProcess = Get-Process -Id $processId -ErrorAction Stop
                                $currentMemory = $currentProcess.WorkingSet64
                                if ($currentMemory -gt $peakMemory) {{
                                    $peakMemory = $currentMemory
                                }}
                                $privilegedTime = $currentProcess.PrivilegedProcessorTime.TotalMilliseconds
                                $userTime = $currentProcess.UserProcessorTime.TotalMilliseconds
                                $hasData = $true
                                Start-Sleep -Milliseconds 100
                            }} catch {{
                                # 进程可能已退出
                                break
                            }}
                        }} while (!$process.HasExited)
                        
                        # 获取最终数据
                        if ($hasData) {{
                            $cpuTime = $process.TotalProcessorTime.TotalMilliseconds
                            Write-Output "$cpuTime $privilegedTime $peakMemory"
                        }} else {{
                            Write-Output "0 0 0"
                        }}
                    }} catch {{
                        Write-Output "0 0 0"
                    }}
                    "#,
                    python_exe_path_manual,
                    file_path_manual, 
                    input_path_manual, 
                    result_path_manual
                );
                Command::new("powershell")
                    .args(&["-Command", &powershell_command])
                    .output()
                    .expect("执行PowerShell命令失败")
                    
            } else {
                // Linux: 使用 `/usr/bin/time` 捕获资源使用情况
                let time_command = format!(
                    "/usr/bin/time -f '%U %S %M' {} {} <{} >{} 2>&1",
                    python_exe_path_manual, file_path_manual, input_path_manual, result_path_manual
                );
                Command::new("sh")
                    .arg("-c")
                    .arg(&time_command)
                    .output()
                    .expect("执行time命令失败")
            }
        }).await {
            Ok(output) => output,
            Err(_) => {
                // 超时处理
                let current_detail = Detail {
                    score: 0, // 超时得分为 0
                    state: OJ_TL.parse().unwrap(), // 超时
                    user_time_usage: "0".to_string(),
                    system_time_usage: "0".to_string(),
                    max_memory_usage: "0".to_string(),
                };
                detail_result.push(current_detail);
                loop_count += 1;
                continue; // 跳过本次循环，继续下一个测试用例
            }
        };
        
        // 解析资源使用情况
        let (user_time_usage, system_time_usage, max_memory_usage) = if cfg!(target_os = "windows") {
            let output_str = String::from_utf8_lossy(&output.stdout);
            let metrics: Vec<&str> = output_str.trim().split_whitespace().collect();
            
            let user_time_usage;
            let system_time_usage;
            let max_memory_usage;
            match metrics.len() {
                1 => {
                    user_time_usage = metrics[0].to_string();
                    system_time_usage = "0".to_string();
                    max_memory_usage = "0".to_string();
                }
                2 => {
                    user_time_usage = metrics[0].to_string();
                    system_time_usage = metrics[1].to_string();
                    max_memory_usage = "0".to_string();
                }
                3 => {
                    user_time_usage = metrics[0].to_string();
                    system_time_usage = metrics[1].to_string();
                    max_memory_usage = metrics[2].to_string();
                }
                _ => {
                    user_time_usage = "0".to_string();
                    system_time_usage = "0".to_string();
                    max_memory_usage = "0".to_string();
                }
            }
            (user_time_usage, system_time_usage, max_memory_usage)
        } else {
            let output_str = String::from_utf8_lossy(&output.stderr);
            let metrics: Vec<&str> = output_str.trim().split_whitespace().collect();
            if metrics.len() < 3 {
                let current_detail = Detail {
                    score: 0, // 运行错误得分为 0
                    state: OJ_RE.parse().unwrap(), // 运行错误
                    user_time_usage: "0".to_string(),
                    system_time_usage: "0".to_string(),
                    max_memory_usage: "0".to_string(),
                };
                detail_result.push(current_detail);
                loop_count += 1;
                continue; // 跳过本次循环，继续下一个测试用例
            }
            let user_time_ms = (metrics[0].parse::<f64>().unwrap() * 1000.0).to_string(); // 用户态时间
            let system_time_ms = (metrics[1].parse::<f64>().unwrap() * 1000.0).to_string(); // 内核态时间
            (user_time_ms, system_time_ms, metrics[2].to_string())
        };
        
        // 检查输出是否匹配
        let mut output_content = match fs::read(result_path_manual.clone()) {
            Ok(bytes) => {
                // 尝试将二进制数据转换为UTF-8字符串
                let (mut decoded, _, had_errors) = UTF_8.decode(&bytes);
                if had_errors {
                    // 判断是否为UTF-16数据
                    let (decoded_16, _, had_errors) = GBK.decode(&bytes);
                    if had_errors {
                        let current_detail = Detail {
                            score: 0, // 运行错误得分为 0
                            state: OJ_RE.parse().unwrap(), // 运行错误
                            user_time_usage,
                            system_time_usage,
                            max_memory_usage,
                        };
                        detail_result.push(current_detail);
                        loop_count += 1;
                        continue; // 跳过本次循环，继续下一个测试用例
                    };
                    // 处理UTF-16数据的情况，转为UTF-8数据
                    decoded = decoded_16;
                }
                decoded.to_string()
            },
            Err(e) => {
                println!("读取执行结果失败 {:?}  :{}", result_path_manual, e);
                let current_detail = Detail {
                    score: 0, // 运行错误得分为 0
                    state: OJ_RE.parse().unwrap(), // 运行错误
                    user_time_usage,
                    system_time_usage,
                    max_memory_usage,
                };
                detail_result.push(current_detail);
                loop_count += 1;
                continue; // 跳过本次循环，继续下一个测试用例
            }
        };
        println!("output_content = {}", output_content);
        // 继续处理output_content
        output_content = output_content.replace("\n", "").replace("\r", "").replace(" ", "");
                
        let output_data = output_data.replace("\n", "").replace("\r", "").replace(" ", "");
        let output_data: String = output_data.chars().filter(|&c| !c.is_control()).collect();

        let state = if output_content == output_data {
            OJ_AC.to_string() // 完全正确
        } else if output_content.replace(|c: char| c.is_whitespace(), "") == output_data.replace(|c: char| c.is_whitespace(), "") {
            OJ_PE.to_string() // 格式错误
        } else { 
            println!("输出不匹配 {} {}", output_content, output_data);
            OJ_WA.to_string() // 答案错误
        };

        // 求其sha2哈希值
        let hash = Sha256::digest(output_content.clone());
        // 转换为字符串
        let tmp_file_name = format!("{:X}.out", hash);
        // 保存文件
        let tmp_file_path = get_path_in_exe_dir("tmp").join("out_data").join(tmp_file_name);
        let tmp_file_path_clone = tmp_file_path.clone().to_str().unwrap().to_string();
        if !tmp_file_path.exists() {
            std::fs::create_dir_all(tmp_file_path.parent().unwrap()).expect("创建目录结构失败");
            std::fs::write(tmp_file_path, output_content.clone()).expect("写入文件失败");
        }
        let inout_data_json = json!({
            "index": loop_count,
            "answer": output_data,
            // "studentAnswer": output_content,
            "stdoutSrc": tmp_file_path_clone,
            "status": (output_content == output_data || output_content.replace(|c: char| c.is_whitespace(), "") == output_data.replace(|c: char| c.is_whitespace(), "")),
        });
        inout_data.push(inout_data_json);
        
        total_score += 100;
        let score = if state == OJ_AC || state == OJ_PE { 100 } else { 0 }; // 只有完全正确得分为 100，其他情况为 0
        pass_count += score;

        let current_detail = Detail {
            score,
            state: state.parse().unwrap(),
            user_time_usage,
            max_memory_usage,
            system_time_usage,
        };
        detail_result.push(current_detail);

        loop_count += 1;
    }
    if loop_count == 1 {
        txn.rollback().await.unwrap();
        return Err(BusinessError::ProcessError { reason: "没有找到测试数据,请联系教师查看课程测试数据是否配置正确".to_string() });
    }
    let inout_data_value = if inout_data.is_empty() {
        serde_json::Value::Array(vec![])
    } else {
        serde_json::Value::Array(inout_data.clone())
    };
    let progress = OjRecord {
        count: loop_count,
        score: pass_count/(loop_count-1),
        detail: detail_result.clone(),
        source: params.source.clone(),
        status: 200,
        history: history,
        message: "全部判决执行完毕".to_string(),
        language: params.language.clone(), 
        inout_data: inout_data_value.clone(),
        solution_type: params.solution_type.clone(),
    };

    // 去数据库中查找记录,存在修改，不存在创建
    let progress_record = match Query::get_progress_by_user(&txn, user_id,params.section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("自系统配置内获取当前记录失败 {}", e.to_string()).to_string() });
        }
    };
    let progress = serde_json::to_value(progress).unwrap();
    // 提交记录
    if progress_record.is_some() {
        
        // 修改提交记录
        let oj_record : OjRecord = serde_json::from_value(progress_record.unwrap()["record"].clone()).unwrap();
        let mut new_history = oj_record.history.clone();
        new_history.push(oj_record.detail.clone());

        let new_oj_record = OjRecord {
            count: loop_count,
            score: pass_count/(loop_count-1),
            detail: detail_result.clone(),
            source: params.source.clone(),
            status: 200,
            history: new_history,
            message: "全部判决执行完毕".to_string(),
            language: params.language.clone(), 
            inout_data: inout_data_value,
            solution_type: params.solution_type.clone(),
        };
        // oj_result = serde_json::to_value(new_oj_record.detail.clone()).unwrap();
        oj_result = serde_json::to_value(new_oj_record.clone()).unwrap();

        let new_oj_record: Value = serde_json::to_value(new_oj_record).unwrap();
        
        
        // 
        match Mutation::update_progress_by_user(&txn, user_id,params.section_id,&new_oj_record, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::ProcessError { reason: format!("更新记录失败 {}", e.to_string()).to_string() });
            }
        };
    }
    else {
        oj_result = serde_json::to_value(progress.clone()).unwrap();

        // 创建提交记录
        match Mutation::create_progress_by_user(&txn, user_id,params.section_id,&progress, total_score, pass_count, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::ProcessError { reason: format!("创建记录失败 {}", e.to_string()).to_string() });
            }
        };
    }

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    // 反馈给教师事件
    let notice_message = r#"{{"type":"update"}}"#.to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
    let websocket_server = &app_state.ws_server;
    let _resp = websocket_server.send(msg).await;

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/course/section/oj/submit", user_id, start_time);

    BusinessResponse::ok(oj_result).to_json_result()
}
// 提交 PPT 记录
#[derive(Deserialize,Serialize,Debug)]
#[serde(rename_all = "camelCase")]
pub struct CommitPPTParams{
    pub chapter_name: String,
    // pub class_i_d: i32,
    pub course_slug: String,
    pub section_name: String,
    pub student_answer: StudentAnswer,
    pub section_id: i32,
}

#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct StudentAnswer{
    pub page: i32,
    pub total_pages: i32,
}
#[post("/api/web/course/section/ppt/submit")]
pub async fn commit_ppt_record(  
    session: Session,
    params : web::Json<CommitPPTParams>,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>{
    let conn = &app_state.conn;
    let start_time = Utc::now();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 去数据库中查找记录,存在修改，不存在创建
    let progress_record = match Query::get_progress_by_user(&txn, user_id,params.section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("自系统配置内获取当前记录失败 {}", e.to_string()).to_string() });
        }
    };
    let record = serde_json::to_value(params.student_answer.clone()).unwrap();
    // 提交记录
    if progress_record.is_some() {
        
        match Mutation::update_progress_by_user(&txn, user_id,params.section_id,&record, params.student_answer.total_pages, params.student_answer.page, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::ProcessError { reason: format!("更新记录失败 {}", e.to_string()).to_string() });
            }
        };
    }
    else {


        // 创建提交记录
        match Mutation::create_progress_by_user(&txn, user_id,params.section_id,&record, params.student_answer.total_pages, params.student_answer.page, start_time).await {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();  
                return Err(BusinessError::ProcessError { reason: format!("创建记录失败 {}", e.to_string()).to_string() });
            }
        };
    }

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    // 反馈给教师事件
    let notice_message = r#"{{"type":"update"}}"#.to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
    let websocket_server = &app_state.ws_server;
    let _resp = websocket_server.send(msg).await;

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "/api/web/course/section/ppt/submit", user_id, start_time);
    BusinessResponse::ok(true).to_json_result()
    
}
// 解析填空题答案
fn extract_values(input: String) -> Vec<String> {
    let re = Regex::new(r"__(\w+)").unwrap();
    re.captures_iter(input.as_str())
        .filter_map(|cap| cap.get(1))
        .map(|m| m.as_str().to_string())
        .collect()
}
// 查询提交记录
#[get("/api/web/course/section/commit/record/{section_id}")]
pub async fn get_commit_record(
    session: Session,
    id: web::Path<i32>,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>  {
    let conn = &app_state.conn;
    let start_time = Utc::now();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();
    // 将id转为i32
    let section_id = match id.into_inner().try_into() {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::InternalError { reason: "参数错误".to_string() })
    };

    // 开启事务
       let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 去数据库中查找记录
    let progress_record = match Query::get_progress_by_user(&txn, user_id, section_id).await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前下载次数失败 {}", e.to_string()).to_string() });
        }
    };


    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    // 反馈给教师事件
    let notice_message = r#"{{"type":"update"}}"#.to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
    let websocket_server = &app_state.ws_server;
    let _resp = websocket_server.send(msg).await;

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "//api/web/course/{courseSlug}/section/{sectionID}/oi/submit", user_id, start_time);
    
    // // 将记录转换为json格式,且避免转义发生
    // let record_json = serde_json::to_value(progress_record).unwrap();
    BusinessResponse::ok(progress_record).to_json_result()
}


// 获取节内容及类型
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct QuerySectionContentAndType {
    course_slug: String,
    chapter_name: String,
    section_name: String,
    if_load_code: bool,
    team_id: Option<i32>
}
#[get("/api/course/querySectionContentAndType")]
async fn query_section_content_and_type(
    session: Session,
    app_state: web::Data<AppState>,
    params: web::Query<QuerySectionContentAndType>
) -> Result<HttpResponse, BusinessError> {
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };
    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    } 
    let user_id = user_id.unwrap();

    // let user_admin_authority = match session.get::<Value>("user_admin_authority") {
    //     Ok(r) => r,
    //     Err(_) => None,
    // };

    let mut is_admin = true;
    // if user_admin_authority.is_none() {
    //     is_admin = false;
    // }
    
    let course_slug = params.course_slug.clone();
    let chapter = params.chapter_name.clone();
    let section = params.section_name.clone();
    let if_load_code = params.if_load_code.clone();
    let team_id = params.team_id.clone();

    // 获取对应course文件路径
    let course_path = get_path_in_exe_dir("course");
    let course_path = course_path.join(course_slug.clone()).join("course.json");

    // submitRecord
    let submit_record = json!({});
    
    // 解析目录文件，拼接对应字段
    let mut chapter_name = String::new();
    let mut section_name = String::new();
    let mut section_type = String::new();
    let mut section_id = 0;
    
    let file_content = fs::read_to_string(course_path).expect("加载课程目录文件失败");
    let course_content: Value = serde_json::from_str(&file_content).unwrap();
    let mut ext = String::new();
    let mut status = None;

    //遍历course_content中的indics字段，获取必要信息
    for value in course_content["indics"].as_array().unwrap() {
        // 获取chapter_name
        if value["chapterName"] == chapter {
            chapter_name = format!("{}.{}", value["chapterIndex"],value["chapterName"]).replace("\"", "");
            // 获取section_name
            for section_value in value["sections"].as_array().unwrap() {
                if section_value["sectionName"] == section {
                    section_name = format!("{}.{}", section_value["sectionIndex"],section_value["sectionName"]).replace("\"", "");
                    section_id = section_value["sectionID"].as_i64().unwrap() as i32;
                    ext = section_value["ext"].as_str().unwrap().to_string();
                    section_type = section_value["sectionType"].as_str().unwrap().to_string();
                    status = section_value["status"].as_bool();
                }
            }
        }
    }
    // 获取课程内容文件
    let content_path = get_path_in_exe_dir("course");
    let content_path = content_path.to_str().unwrap();
    let path = format!("{}/{}/{}/{}.{}", content_path,course_slug , chapter_name , section_name , ext);
    
    let file_info = fs::metadata(path).unwrap();
    let mtime = file_info.modified().unwrap();
    let mtime_ms = mtime.duration_since(UNIX_EPOCH).unwrap().as_secs_f64() * 1_000.0;
    let mtime_datetime = DateTime::<Utc>::from(mtime).format("%Y-%m-%dT%H:%M:%S%.3fZ").to_string();
    let stat = json!({
        "mtime": mtime_datetime,
        "mtime_ms": mtime_ms,        
    });

    


    // content
    let content_path = get_path_in_exe_dir("course");
    let content_path = content_path.to_str().unwrap();
    let content_path = format!("{}/{}/{}/{}.{}", content_path,course_slug , chapter_name , section_name , ext);

    // 按照对应地址读取文件
    let mut course_content: Value = serde_json::Value::Array(Vec::new());
    // 获取文件中的JSON数据
    match section_type.clone().as_str() {
        "OJ" => {
            let file_content = fs::read_to_string(content_path).expect("加载课程目录文件失败");
            let mut content = serde_json::from_str::<OJContent>(&file_content).unwrap();
            // println!("{:?}",content);
                            // // 创建目录
                            // let file_path = get_path_in_exe_dir("data").join(course_slug.clone()).join(chapter.clone()).join(section.clone());
                            // let _ = create_dir_all(&file_path).await;
                            // // 写入input文件
                            // let input_file_path = file_path.join(format!("{}.in", test_count.to_string()));
                            // let mut input_file = File::create(input_file_path.clone()).expect("Unable to create file");
                            // input_file.write_all(input_info.as_bytes()).expect("Unable to write data");
                            // // 写入output文件
                            // let output_file_path = file_path.join(format!("{}.out", test_count.to_string()));
                            // let mut output_file = File::create(output_file_path.clone()).expect("Unable to create file");
                            // output_file.write_all(output_info.as_bytes()).expect("Unable to write data");
                            // // 修改格式
                            // let input_file_path = input_file_path.to_str().unwrap().to_string();
                            // let output_file_path = output_file_path.to_str().unwrap().to_string();
                            // content.judge_menu.test_files.push(TestFile {
                            //     r#type: "inout".to_string() ,
                            //     file_name: test_count.to_string(),
                            //     r#in: input_file_path,
                            //     out: output_file_path,
                            // });
            // 创建目录
            let file_path = get_path_in_exe_dir("data").join(course_slug.clone()).join(chapter.clone()).join(section.clone());
            let _ = create_dir_all(&file_path).await;
            
            // 收集当前有效的测试文件名
            let mut valid_test_files = std::collections::HashSet::new();
            
            // 先更新所有的 inout 文件
            for test_file in &mut content.judge_menu.test_files {
                if test_file.r#type == "inout" {
                    let test_count = test_file.file_name.clone();
                    valid_test_files.insert(test_count.clone());
                    
                    // 写入input文件
                    let input_file_path = file_path.join(format!("{}.in", test_count.to_string()));
                    let mut input_file = File::create(input_file_path.clone()).expect("Unable to create file");
                    input_file.write_all(test_file.r#in.clone().as_bytes()).expect("Unable to write data");
                    
                    // 写入output文件
                    let output_file_path = file_path.join(format!("{}.out", test_count.to_string()));
                    let mut output_file = File::create(output_file_path.clone()).expect("Unable to create file");
                    output_file.write_all(test_file.out.clone().as_bytes()).expect("Unable to write data");
                    
                    // 修改格式
                    let input_file_path = input_file_path.to_str().unwrap().to_string();
                    let output_file_path = output_file_path.to_str().unwrap().to_string();
                    test_file.r#in = input_file_path;
                    test_file.out = output_file_path;
                }
            }
            
            // 删除冗余文件
            if let Ok(entries) = std::fs::read_dir(&file_path) {
                for entry in entries {
                    if let Ok(entry) = entry {
                        let file_name = entry.file_name();
                        let file_name_str = file_name.to_string_lossy().to_string();
                        
                        // 检查是否是 .in 或 .out 文件
                        if let Some(base_name) = file_name_str.strip_suffix(".in") {
                            if !valid_test_files.contains(base_name) {
                                if let Err(e) = std::fs::remove_file(entry.path()) {
                                    eprintln!("删除冗余文件失败: {}", e);
                                }
                            }
                        } else if let Some(base_name) = file_name_str.strip_suffix(".out") {
                            if !valid_test_files.contains(base_name) {
                                if let Err(e) = std::fs::remove_file(entry.path()) {
                                    eprintln!("删除冗余文件失败: {}", e);
                                }
                            }
                        }
                    }
                }
            }
                          
        
            let oj = OJData {
                r#type: "OJ".to_string(),
                content,
                stat: HashMap::new(),
                status: true,
            };

            course_content = serde_json::to_value(oj).unwrap();

            
        }
        "PPT" => {
            let file_content = fs::read_to_string(content_path).expect("加载课程目录文件失败");
            let mut ppt_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
            // 插入课程类型
            match ppt_content {
                Value::Object(ref mut object) => {
                    // 如果 train_course 是一个对象，直接添加新属性    
                    object.insert("type".to_string(), serde_json::Value::String(section_type.clone()));
                }
                _ => {
                    
                }
            }
            course_content = json!({
                "type": section_type,
                "content": ppt_content
            });

        },
        _ => {
            let file_content = fs::read_to_string(content_path).expect("加载课程目录文件失败");
            course_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
            // 插入课程类型
            match course_content {
                Value::Object(ref mut object) => {
                    // 如果 train_course 是一个对象，直接添加新属性
                    object.insert("type".to_string(), serde_json::Value::String(section_type.clone()));
                }
                _ => {
                    
                }
            }
        }
    }
    
    // 
    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    

    // 读取账号是否存在
    let user_record = match Query::user_find_by_id(&txn, user_id).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
        }
    };
    
    let user_admin_authority = user_record.unwrap().admin_authority;

    // let mut is_admin = true;
    if user_admin_authority.is_none() {
        is_admin = false;
    }
    let course_info_item = match Query::course_info_find_by_slug_json(&txn, course_slug.clone()).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("查找课程信息失败 {}", e.to_string()).to_string() })
        }
    };

    let mut course_id ;
    let mut save_code ;
    let mut save_run_result ;
    let mut container_info = None;

    if let Some(course_info_item) = course_info_item {
        course_id = course_info_item.id;
        save_code = course_info_item.save_code;
        save_run_result = course_info_item.save_run_result;
        container_info = course_info_item.container_info;
    } else {
        txn.rollback().await.unwrap();
        return Err(BusinessError::InternalError { reason: "查找课程信息失败".to_string() })
    }    
    

    let teams = match Query::all_team_find(&txn).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("查找班级失败 {}", e.to_string()).to_string() })
        }
    };

    // record
    let user_record = match Query::user_record_find_by_section_id_and_user_id(&txn, user_id.clone(), section_id).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("查找记录失败{}", e.to_string()).to_string() })
        }       
    };

    let mut record = Option::None;
    // let mut created_at;
    // let mut updated_at;
    let mut record_time = Value::Null;
    let mut total_score = 0;
    let mut pass_count = 0;
    if let Some(user_record) = user_record {
        record = user_record.record;
        let record_times_json = json!({
            "create": user_record.created_at,
            "update": user_record.updated_at
        });
        record_time = record_times_json;
        total_score = user_record.total_score;
        pass_count = user_record.pass_count;
    }



    let mut record_res: HashMap<String, Value> = HashMap::new();
    let mut record_message = json!({});
    match section_type.as_str() {
        "AI" => {
            if let Some(record) = record.clone() {
                let record_map: HashMap<String, Value> = serde_json::from_value(record).unwrap();

                for (uuid, source_params) in record_map {
                    let sourse_type = source_params["microAppFileType"]
                    .as_str()
                    .unwrap_or_else(|| {
                        record_res.insert(uuid.clone(), source_params.clone());
                        ""
                    });
                    let full_ext = match sourse_type {
                        "text" => {
                            "txt"
                        },
                        "table" => {
                            "csv"
                        },
                        "mind" | "flow" | "networksimulator" | "spreadsheet" | "drawio" | "Scratch" | "MicroBit" => {
                            "json"
                        }
                        _ => {
                            continue;
                        }
                    };
                    let full_name = format!("{}.{}", uuid, full_ext);
                    let file_path = get_path_in_exe_dir("static").join("student").join("course").join(course_slug.clone()).join(user_id.to_string()).join(chapter.clone()).join(full_name.clone());
                    if file_path.exists() {
                        let file_content = fs::read_to_string(file_path.clone()).expect("加载课程记录文件失败");
                        
                        let message;
                        if sourse_type == "table" {
                            let cells: Vec<Value> = serde_json::from_value(course_content["cells"].clone()).unwrap();
                            let mut cell_info: Vec<Vec<String>> =Vec::new();
                            for cell in cells {
                                let cell_uuid = cell["metadata"]["UUID"].as_str().unwrap();
                                if cell_uuid == uuid {
                                    let cell_data = cell["metadata"]["config"]["cell"].clone();
                                    let processed_cell_info = match cell_data {
                                        Value::Array(arr) => {
                                            let processed: Vec<Value> = arr.into_iter().map(|item| {
                                                if item.is_null() {
                                                    Value::Array(vec![]) // 将 null 转换为空数组
                                                } else {
                                                    item
                                                }
                                            }).collect();
                                            Value::Array(processed)
                                        }
                                        _ => cell_data
                                    };

                                    cell_info = match serde_json::from_value(processed_cell_info) {
                                        Ok(info) => info,
                                        Err(e) => {
                                            eprintln!("Failed to deserialize cell_info: {}", e);
                                            return Err(BusinessError::InternalError { 
                                                reason: format!("数据解析失败: {}", e) 
                                            });
                                        }
                                    };
                                }
                            }

                            let file_content = serde_json::to_vec(&file_content).unwrap();
                            let file_content = match str::from_utf8(&file_content) {
                                Ok(v) => v.trim_matches('"').to_string(),
                                Err(e) => panic!("Invalid UTF-8 sequence: {}", e),
                            };
                            let file_content: Vec<String> = file_content.split("\\n").map(String::from).collect();
                            if !file_content.is_empty() {
                                let result = file_content
                                    .iter()
                                    .enumerate()
                                    .map(|(i, data)| {
                                        data.split(',')
                                            .enumerate()
                                            .map(|(j, v)| json!({
                                                "readOnly": if i < cell_info.len() && j < cell_info[i].len() {
                                                    true
                                                } else {
                                                    false
                                                },
                                                "style": {"background": "rgb(238, 238, 238)"},
                                                "value": v,
                                            }))
                                            .collect::<Vec<Value>>()
                                    })
                                    .collect::<Vec<Vec<Value>>>();
                        
                                message = json!(result);
                            } else {
                                message = json!([]);
                            }
                        }else {
                            message = serde_json::from_str(&file_content).expect("解析课程记录文件失败");
                        }
                        
                        let code_result: Value = json!({
                            "fileNanme": uuid,
                            "message": message,
                            //source_params
                            "type": sourse_type
                        });
                        let mut code_result_vec: Vec<Value> =Vec::new();
                        // println!("code_result: {:?}", code_result_vec);
                        code_result_vec.push(code_result);
                        let turtle_models: Vec<Value> = Vec::new();
                        let result = json!({
                            "codeResult": code_result_vec,
                            "turtleModels": turtle_models
                        });
                        let file_info = fs::metadata(file_path).unwrap();
                        let mtime = file_info.modified().unwrap();
                        let mtime_ms = mtime.duration_since(UNIX_EPOCH).unwrap().as_secs_f64() * 1_000.0;
                        let mtime_datetime = DateTime::<Utc>::from(mtime).format("%Y-%m-%dT%H:%M:%S%.3fZ").to_string();
                        let stat = json!({
                            "mtime": mtime_datetime,
                            "mtime_ms": mtime_ms,        
                        });
                        let code_result = json!({
                            "fileStat": stat,
                            "fullName": full_name,
                            "result": result,
                            "status": "运行通过",
                            "type": "success"
                        });
                        record_res.insert(uuid.clone(), code_result);
                    }
                }
                record_message = serde_json::to_value(record_res).unwrap();
            } 
        }
        "OI" => {
            if let Some(record) = record.clone() {
                let mut record_map: HashMap<String, Value> = serde_json::from_value(record).unwrap();
                record_map.insert("totalScore".to_string(), json!(total_score) );
                record_map.insert("passCount".to_string(), json!(pass_count) );
                record_message = serde_json::to_value(record_map).unwrap();
            }
        },
        "OJ" | "CodeBlank" | "PPT" | "Access" | "Excel" => { 
            if let Some(record) = record.clone() {
                record_message = record;
            }
        },
        "MicroBit" | "Scratch" => { 
            let full_name = format!("{}.json", section);
            let file_path = get_path_in_exe_dir("static").join("student").join("course").join(course_slug.clone()).join(user_id.to_string()).join(chapter.clone()).join(full_name.clone());
            if file_path.exists() {
                let file_content = fs::read_to_string(file_path.clone()).expect("加载课程记录文件失败");
                let message: Value = serde_json::from_str(&file_content).expect("解析课程记录文件失败");
                let result: Value = json!({
                    "fileNanme": section,
                    "message": message,
                });
                let file_info = fs::metadata(file_path).unwrap();
                let mtime = file_info.modified().unwrap();
                let mtime_ms = mtime.duration_since(UNIX_EPOCH).unwrap().as_secs_f64() * 1_000.0;
                let mtime_datetime = DateTime::<Utc>::from(mtime).format("%Y-%m-%dT%H:%M:%S%.3fZ").to_string();
                let stat = json!({
                    "mtime": mtime_datetime,
                    "mtime_ms": mtime_ms,        
                });
                let code_result = json!({
                    "fileStat": stat,
                    "fullName": full_name,
                    "result": result,
                    "status": "运行通过",
                    "type": "success"
                });
                record_message = code_result;

            }
        },
        _ => {
        }
        
    }

    let mut section_records:Vec<SectionRecordItem> = Vec::new();
    let mut student_ids:Vec<i32> = Vec::new();
    // statisResult students
    if let Some(team_id) = team_id {
        (section_records, student_ids) = match Query::section_record_find_by_course_id_and_section_id(&txn, team_id, section_id).await  {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::InternalError { reason: format!("查找记录失败{}", e.to_string()).to_string() })
            }       
        };
    }
    let mut static_result =  json!({});
    match section_type.as_str() {
        "AI" => {
            let mut question_map: HashMap<String, Value> = HashMap::new();
            if let Some(record) = record.clone() {
                let record_map: HashMap<String, Value> = serde_json::from_value(record).unwrap();
                for (uuid, _) in record_map {
                    let mut error_arr: Vec<i32> = Vec::new();
                    let mut notpass_arr: Vec<i32> = student_ids.clone();
                    let mut pass_arr: Vec<i32> = Vec::new();
                    for section_record in section_records.clone() {
                        if let Some(record_data) = section_record.record {
                            let record_data_map: HashMap<String, Value> = serde_json::from_value(record_data).unwrap();
                            if let Some(record_data_result) = record_data_map.get(&uuid) {
                                if let Some(result) = record_data_result.get("type") {
                                    match result.as_str().unwrap() {
                                        "success" => {
                                            notpass_arr.retain(|&x| x != section_record.user_id);
                                            pass_arr.push(section_record.user_id);
                                        },
                                        "error" => {
                                            notpass_arr.retain(|&x| x != section_record.user_id);
                                            error_arr.push(section_record.user_id);
                                        },
                                        "notRun" => {
                                        },
                                        _ => {
                                            
                                        }
                                    }
                                }
                            }
                        }
                    }
                    let question_res = json!({
                        "error": error_arr,
                        "notPass": notpass_arr,
                        "pass": pass_arr
                    });
                    question_map.insert(uuid, question_res);
                }
                static_result = serde_json::to_value(question_map).unwrap();
            }  else if team_id.is_some() {
                let cells = serde_json::from_value::<Vec<Value>>(course_content["cells"].clone()).unwrap();
                for cell in cells {
                    
                    let cell_type = cell["cell_type"].as_str().unwrap().to_string();
                    if cell_type != "code".to_string() { 
                        continue;
                    }
                    let uuid = cell["metadata"]["UUID"].as_str().unwrap().to_string();

                    let mut error_arr: Vec<i32> = Vec::new();
                    let mut notpass_arr: Vec<i32> = student_ids.clone();
                    let mut pass_arr: Vec<i32> = Vec::new();
                    for section_record in section_records.clone() {
                        if let Some(record_data) = section_record.record {
                            let record_data_map: HashMap<String, Value> = serde_json::from_value(record_data).unwrap();
                            println!("{}", uuid);
                            if let Some(record_data_result) = record_data_map.get(&uuid) {
                                println!("{}", record_data_result);
                                if let Some(result) = record_data_result.get("type") {
                                    match result.as_str().unwrap() {
                                        "success" => {
                                            notpass_arr.retain(|&x| x != section_record.user_id);
                                            pass_arr.push(section_record.user_id);
                                        },
                                        "error" => {
                                            notpass_arr.retain(|&x| x != section_record.user_id);
                                            error_arr.push(section_record.user_id);
                                        },
                                        "notRun" => {
                                        },
                                        _ => {
                                            
                                        }
                                    }
                                }
                            }
                        }
                    }
                    let question_res = json!({
                        "error": error_arr,
                        "notPass": notpass_arr,
                        "pass": pass_arr
                    });
                    question_map.insert(uuid, question_res);
                }
                static_result = serde_json::to_value(question_map).unwrap();

            }
            

        }
        "OI" | "CodeBlank" => {
            let mut question_map: HashMap<String, Value> = HashMap::new();

            if let Some(record) = record.clone() {
                let record_map: HashMap<String, Value> = serde_json::from_value(record).unwrap();
                for (uuid, _) in record_map {
                    if uuid.contains("submitTimes") {
                        continue;
                    }
                    let mut error_arr: Vec<i32> = Vec::new();
                    let mut notpass_arr: Vec<i32> = student_ids.clone();
                    let mut pass_arr: Vec<i32> = Vec::new();
                    for section_record in section_records.clone() {
                        if let Some(record_data) = section_record.record {
                            let record_data_map: HashMap<String, Value> = serde_json::from_value(record_data).unwrap();
                            if let Some(record_data_result) = record_data_map.get(&uuid) {
                                if let Some(result) = record_data_result.get("status") {
                                    match result.as_bool().unwrap() {
                                        true => {
                                            notpass_arr.retain(|&x| x != section_record.user_id);
                                            pass_arr.push(section_record.user_id);
                                        },
                                        false => {
                                            notpass_arr.retain(|&x| x != section_record.user_id);
                                            error_arr.push(section_record.user_id);
                                        },
                                    }
                                }
                            }
                        }
                    }
                    let question_res = json!({
                        "error": error_arr,
                        "notPass": notpass_arr,
                        "pass": pass_arr
                    });
                    question_map.insert(uuid, question_res);
                }
                static_result = serde_json::to_value(question_map).unwrap();
            } else if team_id.is_some() {
                let student_ids = match Query::user_id_find_by_team_ids(&txn, team_id.unwrap()).await{
                    Ok(ids) => ids,
                    Err(e) => {
                        // static_result = json!({});
                        // return;
                        return Err(BusinessError::ProcessError { reason: format!("班级成员信息有误，请联系管理员 {}", e.to_string()).to_string() })
                    }
                };




                if section_type.as_str() == "OI" { 
                    let questions = serde_json::from_value::<Vec<Value>>(course_content["questions"].clone()).unwrap();
                    // 收集questions中每一个questionType不是文本的元素的uuid字段
                    let question_uuids: Vec<String> = questions
                        .iter()
                        .filter(|question| question["questionType"] != "文本")
                        .map(|question| question["UUID"]
                            .as_str()
                            .unwrap()
                            .to_string())
                        .collect();


                    for uuid in question_uuids {
                        let mut error_arr: Vec<i32> = Vec::new();
                        let mut notpass_arr: Vec<i32> = student_ids.clone();
                        let mut pass_arr: Vec<i32> = Vec::new();
                        for section_record in section_records.clone() {
                            if let Some(record_data) = section_record.record {
                                let record_data_map: HashMap<String, Value> = serde_json::from_value(record_data).unwrap();
                                if let Some(record_data_result) = record_data_map.get(&uuid) {
                                    if let Some(result) = record_data_result.get("status") {
                                        match result.as_bool().unwrap() {
                                            true => {
                                                notpass_arr.retain(|&x| x != section_record.user_id);
                                                pass_arr.push(section_record.user_id);
                                            },
                                            false => {
                                                notpass_arr.retain(|&x| x != section_record.user_id);
                                                error_arr.push(section_record.user_id);
                                            },
                                        }
                                    }
                                }
                            }
                        }

                        let question_res = json!({
                            "error": error_arr,
                            "notPass": notpass_arr,
                            "pass": pass_arr
                        });
                        question_map.insert(uuid, question_res);
                    }
                    static_result = serde_json::to_value(question_map).unwrap();
                } else {
                    let questions = serde_json::from_value::<Vec<Value>>(course_content["questions"].clone()).unwrap();
                    let answers_list = serde_json::from_value::<HashMap<String, Vec<Value>>>(questions[0]["answer"].clone()).unwrap();
                    // 获取所有key
                    let keys = answers_list.keys().cloned().collect::<Vec<_>>();


                    for key in keys {
                        let mut error_arr: Vec<i32> = Vec::new();
                        let mut notpass_arr: Vec<i32> = student_ids.clone();
                        let mut pass_arr: Vec<i32> = Vec::new();
                        for section_record in section_records.clone() {
                            if let Some(record_data) = section_record.record {
                                let record_data_map: HashMap<String, Value> = serde_json::from_value(record_data).unwrap();
                                if let Some(record_data_result) = record_data_map.get(&key) {
                                    if let Some(result) = record_data_result.get("status") {
                                        match result.as_bool().unwrap() {
                                            true => {
                                                notpass_arr.retain(|&x| x != section_record.user_id);
                                                pass_arr.push(section_record.user_id);
                                            },
                                            false => {
                                                notpass_arr.retain(|&x| x != section_record.user_id);
                                                error_arr.push(section_record.user_id);
                                            },
                                        }
                                    }
                                }
                            }
                        }
                        let question_res = json!({
                            "error": error_arr,
                            "notPass": notpass_arr,
                            "pass": pass_arr
                        });
                        question_map.insert(key, question_res);
                    }
                    static_result = serde_json::to_value(question_map).unwrap();
                }

            }
        }
        "OJ" => {
            let mut error_arr: Vec<i32> = Vec::new();
            let mut notpass_arr: Vec<i32> = student_ids.clone();
            let mut pass_arr: Vec<i32> = Vec::new();
            
            for section_record in section_records.clone() {
                if let Some(record_data) = section_record.record {
                    let record_data_map: HashMap<String, Value> = serde_json::from_value(record_data).unwrap();
                    if let Some(result) = record_data_map.get("score") {
                        match result.as_i64().unwrap() {
                            100 => {
                                notpass_arr.retain(|&x| x != section_record.user_id);
                                pass_arr.push(section_record.user_id);
                            },
                            _ => {
                                notpass_arr.retain(|&x| x != section_record.user_id);
                                error_arr.push(section_record.user_id);
                            },
                        }
                    }
                }
            }
            let question_res = json!({
                "error": error_arr,
                "notPass": notpass_arr,
                "pass": pass_arr
            });
            static_result = serde_json::to_value(question_res).unwrap();
        }
        "Scratch" => {
            let judge_steps: std::result::Result<Vec<Value>, serde_json::Error> = serde_json::from_value(course_content["judgeSteps"].clone());
            let judge_steps = judge_steps.unwrap();

            // 获取该班级所有学生id
            if team_id.is_some() {
                let student_ids = match Query::user_id_find_by_team_ids(&txn, team_id.unwrap()).await{
                    Ok(ids) => ids,
                    Err(e) => {
                        // static_result = json!({});
                        // return;
                        return Err(BusinessError::ProcessError { reason: format!("班级成员信息有误，请联系管理员 {}", e.to_string()).to_string() })
                    }
                };
                // 计算代码块，初始化数据
                let mut uuid_list: HashMap<String, HashMap<String, Vec<i32>>> = HashMap::new();
                let mut res_model: HashMap<String, Vec<i32>> = HashMap::new();
                res_model.insert("notPass".to_string(), student_ids);
                res_model.insert("pass".to_string(), Vec::new());
                res_model.insert("error".to_string(), Vec::new());
                for step in judge_steps {
                    let uuid = step["uuid"].as_str().unwrap().to_string();
                    uuid_list.insert(uuid, res_model.clone());
                }
                // 处理用户记录
                for section_record in section_records.clone() {
                    let record_data = section_record.record;
                    let cur_user_id = section_record.user_id;
                    if record_data.is_none() {
                        continue;
                    }
                    let record_data = record_data.unwrap();
                    let judge_steps_record = record_data["judge_steps"].clone();
                    println!("judge_steps_record: {:?}", judge_steps_record);
                    let judge_steps_record: Vec<Value> = serde_json::from_value(judge_steps_record).unwrap();

                    for step_record in judge_steps_record {
                        let uuid = step_record["uuid"].as_str().unwrap().to_string();
                        let manual: std::result::Result<bool, serde_json::Error> = serde_json::from_value(step_record["manual"].clone());
                        let manual = manual.unwrap();
                        let pass;
                        if manual { 
                            let rate = step_record["rate"].clone();
                            let score = step_record["score"].clone();
                            pass = if score == rate { true } else { false };
                        } else {
                            pass = step_record
                                .get("pass")
                                .and_then(|v| v.as_bool())
                                .unwrap_or(false); 
                        }


                        // 根据pass状态更新对应uuid的统计
                        if let Some(uuid_entry) = uuid_list.get_mut(&uuid) {
                            if pass {
                                // 如果通过，添加到pass列表
                                uuid_entry.get_mut("pass").unwrap().push(cur_user_id);
                                // 并且从not_pass列表中删除
                                uuid_entry.get_mut("notPass").unwrap().retain(|x| *x != cur_user_id);
                            } else {
                                // 如果通过，添加到pass列表
                                uuid_entry.get_mut("error").unwrap().push(cur_user_id);
                                // 并且从not_pass列表中删除
                                uuid_entry.get_mut("notPass").unwrap().retain(|x| *x != cur_user_id);
                            }
                        }
                    }
                }
                // 将uuid_list转为json
                static_result = json!(uuid_list);                
            } else {
                static_result = json!({});
            }
        }
        "Access" | "Excel" => {
            if team_id.is_some() {
                let student_ids = match Query::user_id_find_by_team_ids(&txn, team_id.unwrap()).await{
                    Ok(ids) => ids,
                    Err(e) => {
                        // static_result = json!({});
                        // return;
                        return Err(BusinessError::ProcessError { reason: format!("班级成员信息有误，请联系管理员 {}", e.to_string()).to_string() })
                    }
                };
                let mut res_model: HashMap<String, Vec<i32>> = HashMap::new();
                res_model.insert("notPass".to_string(), student_ids);
                res_model.insert("pass".to_string(), Vec::new());
                res_model.insert("error".to_string(), Vec::new());

                // 处理用户记录
                for section_record in section_records.clone() {
                    let cur_user_id = section_record.user_id;
                    let record_data = section_record.record;
                    if record_data.is_none() {
                        continue;
                    }
                    let record_data = record_data.unwrap();
                    let steps_record = record_data["steps"].clone();
                    let steps_record: Vec<Value> = serde_json::from_value(steps_record).unwrap();
                    let mut pass = true;
                    for step_record in steps_record {
                        let status = step_record["status"].as_str().unwrap();
                        if status == "error" {
                            pass = false;
                            break;
                        }
                    }
                    if pass {
                        // 如果通过，添加到pass列表
                        res_model.get_mut("pass").unwrap().push(cur_user_id);
                        // 并且从not_pass列表中删除
                        res_model.get_mut("notPass").unwrap().retain(|x| *x != cur_user_id);
                    } else {
                        // 如果未通过，添加到not_pass列表
                        res_model.get_mut("error").unwrap().push(cur_user_id);
                        // 删除pass列表中的数据
                        res_model.get_mut("notPass").unwrap().retain(|x| *x != cur_user_id);
                    }                    
                }
                static_result = json!(res_model);    
            } else {
                static_result = json!({});
            }
        }       
        _ => {
            static_result = json!({});
        }
    }
    


    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };

    let mut merged_fields = course_content.as_object().unwrap().clone();

    merged_fields.insert("containerInfo".to_string(), json!(container_info));
    merged_fields.insert("isAdmin".to_string(), json!(is_admin));
    merged_fields.insert("record".to_string(), json!(record_message));
    merged_fields.insert("recordTime".to_string(), json!(record_time));
    merged_fields.insert("stat".to_string(), json!(stat));
    merged_fields.insert("statisResult".to_string(), json!(static_result));
    merged_fields.insert("status".to_string(), json!(status));
    merged_fields.insert("students".to_string(), json!(student_ids.len()));
    merged_fields.insert("submitRecord".to_string(), json!(submit_record));
    merged_fields.insert("teams".to_string(), json!(teams));
    merged_fields.insert("type".to_string(), json!(section_type));

    BusinessResponse::ok(merged_fields).to_json_result()

}
// 微应用文件重置
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct QueryMicroAppPrimaryContentByUUID {
    course_slug: String,
    chapter_name: String,
    section_name: String,
    full_filename: String,
}
#[get("/api/getMicroAppPrimaryContentByUUID")]
async fn get_micro_app_primary_content_by_uuid(
    session: Session,
    // app_state: web::Data<AppState>,
    params: web::Query<QueryMicroAppPrimaryContentByUUID>
) -> Result<HttpResponse, BusinessError> {
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };
    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    } 
    let user_id = user_id.unwrap();

    let course_slug = params.course_slug.clone();
    let chapter = params.chapter_name.clone();
    let section = params.section_name.clone();
    let full_filename = params.full_filename.clone();
    let mut uuid = String::new();
    let parts: Vec<&str> = full_filename.split('.').collect();

    // 检查分割后的部分数量
    if parts.len() >= 1 {
        // 返回第一个部分（即 UUID）
        uuid = parts[0].to_string();
    }

    // 获取对应course文件路径
    let course_path = get_path_in_exe_dir("course");
    let course_path = course_path.join(course_slug.clone()).join("course.json");


    
    // 解析目录文件，拼接对应字段
    let mut chapter_name = String::new();
    let mut section_name = String::new();
    let mut section_type = String::new();
    let mut section_id = 0;
    
    let file_content = fs::read_to_string(course_path).expect("加载课程目录文件失败");
    let course_content: Value = serde_json::from_str(&file_content).unwrap();
    let mut ext = String::new();
    let mut status = None;

    //遍历course_content中的indics字段，获取必要信息
    for value in course_content["indics"].as_array().unwrap() {
        // 获取chapter_name
        if value["chapterName"] == chapter {
            chapter_name = format!("{}.{}", value["chapterIndex"],value["chapterName"]).replace("\"", "");
            // 获取section_name
            for section_value in value["sections"].as_array().unwrap() {
                if section_value["sectionName"] == section {
                    section_name = format!("{}.{}", section_value["sectionIndex"],section_value["sectionName"]).replace("\"", "");
                    section_id = section_value["sectionID"].as_i64().unwrap() as i32;
                    ext = section_value["ext"].as_str().unwrap().to_string();
                    section_type = section_value["sectionType"].as_str().unwrap().to_string();
                    status = section_value["status"].as_bool();
                }
            }
        }
    }
    // 删除用户文件
    let file_path = get_path_in_exe_dir("static").join("student").join("course").join(course_slug.clone()).join(user_id.to_string()).join(chapter.clone()).join(full_filename.clone());
    if file_path.exists() {
        fs::remove_file(file_path).expect("删除文件失败");
    }
    // 获取原始数据
    let content_path = get_path_in_exe_dir("course");
    let content_path = content_path.to_str().unwrap();
    let content_path = format!("{}/{}/{}/{}.{}", content_path,course_slug , chapter_name , section_name , ext);

    // 按照对应地址读取文件
    // let mut course_content: Value = serde_json::Value::Array(Vec::new());
    // 获取文件中的JSON数据
    let file_content = fs::read_to_string(content_path.clone()).expect("加载课程目录文件失败");
    let course_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
    
    let mut cell_info: Value = json!({});
    match section_type.as_str() {
        "AI" => {
            let cells: Vec<Value> = serde_json::from_value(course_content["cells"].clone()).unwrap();
            for cell in cells {
                let cell_uuid = cell["metadata"]["UUID"].as_str().unwrap();
                if cell_uuid == uuid {
                    cell_info = cell.clone();
                }
            }
        }
        _ => {
            cell_info = course_content;
        }
    }

    BusinessResponse::ok(cell_info).to_json_result()

}

#[derive(Serialize,Debug,Clone)]
struct GroupedCourses {
    grouped: HashMap<String, Vec<Value>>,
    ungrouped: Vec<Value>,
}

// 按照中文数字和上下册排序课程
fn sort_by_chinese_number(courses: &mut [Value]) {
    // 中文数字映射表（支持一到十）
    let chinese_number_map: HashMap<char, i32> = [
        ('一', 1),
        ('二', 2),
        ('三', 3),
        ('四', 4),
        ('五', 5),
        ('六', 6),
        ('七', 7),
        ('八', 8),
        ('九', 9),
        ('十', 10),
    ].iter().cloned().collect();

    // 提取排序键
    let get_sort_key = |display_name: &str| -> i32 {
        // 匹配 "三年级上"、"七年级下" 等格式
        let mut chars = display_name.chars();
        let mut number = 0;
        let mut term = 0;
        
        // 提取中文数字
        while let Some(c) = chars.next() {
            if let Some(&n) = chinese_number_map.get(&c) {
                number = n;
            } else if c == '年' {
                break;
            }
        }
        
        // 提取上下册
        if display_name.contains('上') {
            term = 0;
        } else if display_name.contains('下') {
            term = 1;
        }
        
        number * 10 + term
    };

    courses.sort_by(|a, b| {
        let a_name = a["displayName"].as_str().unwrap_or("");
        let b_name = b["displayName"].as_str().unwrap_or("");
        let a_key = get_sort_key(a_name);
        let b_key = get_sort_key(b_name);
        a_key.cmp(&b_key)
    });
}

fn group_courses(courses: &[Value]) -> GroupedCourses {
    let mut groups: HashMap<String, Vec<Value>> = HashMap::new();
    let mut ungrouped: Vec<Value> = Vec::new();

    for course in courses {
        let name = course["courseName"].as_str().unwrap_or("");
        
        // 使用字符位置处理分隔符
        if let Some((prefix, suffix)) = name.split_once('·') {
            let group_name = prefix.trim().to_string();
            let display_name = suffix.trim().to_string();
            
            // 创建新对象并添加 displayName 字段
            let mut grouped_course = course.clone();
            if let Some(obj) = grouped_course.as_object_mut() {
                obj.insert("displayName".to_string(), Value::String(display_name));
            }
            
            groups.entry(group_name)
                .or_default()
                .push(grouped_course);
        } else {
            ungrouped.push(course.clone());
        }
    }

    // 过滤并排序分组
    let mut valid_groups: Vec<_> = groups.into_iter()
        .filter_map(|(group_name, mut courses)| {
            if courses.len() > 1 {
                sort_by_chinese_number(&mut courses);
                Some((group_name, courses))
            } else {
                ungrouped.extend(courses);  // 将小分组内容加入ungrouped
                None
            }
        })
        .collect();
    
    // 按课程数量降序排序
    valid_groups.sort_by(|(_, a), (_, b)| b.len().cmp(&a.len()));
    
    GroupedCourses {
        grouped: valid_groups.into_iter().collect(),
        ungrouped,
    }
}

// 获取有权限课程具体信息
#[get("/api/admin/course/all")]
pub async fn get_course_all(
    session: Session,
    http_request: HttpRequest,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>  {
    let conn = &app_state.conn;
    let start_time = Utc::now();
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();
    

    // 开启事务
       let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 获取当前可用的课程计划
    let course_list = match Query::query_course_list(&txn).await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("获取当前可用的课程计划失败 {}", e.to_string()).to_string() });
        }
    };
    // println!("{:?}", course_list);

    let current_course_slug = match Query::system_config_find_by_key(&txn, "current_course_slug").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("自系统配置内获取当前课程ID失败 {}", e.to_string()).to_string() });
        }
    };

    // 遍历course_list,过滤出有效信息
    let mut course_list_full = Vec::new();

    for course in course_list {
        let update_at = course["updated_at"].clone();
        let update_at = serde_json::to_string(&update_at).unwrap();
        // 转为时间戳
        let update_at_parsed = chrono::NaiveDateTime::parse_from_str(
            update_at.trim_matches('"'), 
            "%Y-%m-%d %H:%M:%S%.f"
        ).unwrap();
        let update_at_utc = chrono::DateTime::<chrono::Utc>::from_naive_utc_and_offset(update_at_parsed, chrono::Utc);
        let update_at_iso = update_at_utc.to_rfc3339_opts(chrono::SecondsFormat::Millis, true);
        let creater: Value = serde_json::from_str(course["creater"].as_str().unwrap()).unwrap();
        let record_full = json!({
            "id": course["id"],
            "updated_at":update_at_iso,
            // "updated_at":course["updated_at"],
            // "createrID": course["creater_id"],
            "courseDescription":course["course_description"],
            "courseName":course["course_name"],
            "courseSlug": course["course_slug"],
            "courseType":course["course_type"],
            "creater":creater,
            "statist":{
                "students":course["statist"]["students"],
                "chapters": course["statist"]["chapters"]
            },
            "teachers" : course["teachers"],
            "teams" :course["teams"],
            "current_course_slug": current_course_slug.clone()
        });

        


        // 将record_full存入course_list_full
        course_list_full.push(record_full); 
        // println!("{:?}", course_list_full);
    };
    // 排序
    let result = group_courses(&course_list_full);
    let course_list_json = serde_json::to_value(result).expect("Failed to serialize course list");
    // course_list_full.sort_by(|a, b| {
    //     let name_a = a["courseName"].as_str().unwrap_or("");
    //     let name_b = b["courseName"].as_str().unwrap_or("");
        
    //     // 检查是否包含间隔号"·"
    //     let has_dot_a = name_a.contains('·');
    //     let has_dot_b = name_b.contains('·');
        
    //     // 规则1：优先显示带间隔号的课程
    //     if !has_dot_a && has_dot_b {
    //         return std::cmp::Ordering::Less;
    //     } else if has_dot_a && !has_dot_b {
    //         return std::cmp::Ordering::Greater;
    //     }
        
    //     // 提取前缀（"·"前面的部分）
    //     let prefix_a = name_a.split('·').next().unwrap_or("");
    //     let prefix_b = name_b.split('·').next().unwrap_or("");
        
    //     // 规则2：按前缀分组排序
    //     let prefix_cmp = prefix_a.cmp(prefix_b);
    //     if prefix_cmp != std::cmp::Ordering::Equal {
    //         return prefix_cmp;
    //     }
        
    //     // 规则3：相同前缀的按完整名称排序
    //     name_a.cmp(name_b)
    // });

    // let course_list_json = serde_json::to_value(course_list_full).expect("Failed to serialize course list");

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    // 反馈给教师事件
    let notice_message = r#"{{"type":"update"}}"#.to_string();
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: notice_message };
    let websocket_server = &app_state.ws_server;
    let _resp = websocket_server.send(msg).await;

    let connection_info = http_request.connection_info();
    let client_ip = connection_info.peer_addr().unwrap();
    log_request(client_ip, "//api/web/course/all", user_id, start_time);

    BusinessResponse::ok(course_list_json).to_json_result()
}
// 获取所有课程标记
#[get("/api/admin/courseSlug/list")]
pub async fn get_course_slug_list(
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError>  {
    let conn = &app_state.conn;    

    // 开启事务
       let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 获取当前可用的课程计划
    let course_list = match Query::find_course_list(&txn).await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("获取当前可用的课程计划失败 {}", e.to_string()).to_string() });
        }
    };
    
    // 获取course_list所有的course_slug
    let course_slug_list: Vec<String> = course_list.iter().map(|x| x.course_slug.clone()).collect();

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };


    BusinessResponse::ok(course_slug_list).to_json_result()
}
// #[get("/api/admin/course/downloadCourseZIP")]

// 导入课程文件包后创建课程体系
pub async fn create_course_sections(
    course_slug: String,
    conn: &sea_orm::prelude::DatabaseConnection
) ->  bool {
    let start_time = Utc::now();
    // let conn = &conn;

    // 获取课程信息
    let course_info = get_course(course_slug.clone());

    // 为indics新增batchSwitchChapterType，openTeam字段
    let mut indics: Value =course_info["indics"].clone();

    if let Value::Array(chapter) = &mut indics {
        for chapter in chapter {
            if let Value::Object(chapter_obj) = chapter {
                // 添加新字段
                chapter_obj.insert("ChapterIsOpen".to_string(), json!(false));
                // 插入一个数组openTeam
                chapter_obj.insert("chapterOpenTeam".to_string(), json!([]));
                
                if let Some(Value::Array(sections)) = chapter_obj.get_mut("sections") {
                    for section in sections {
                        if let Value::Object(section_obj) = section {
                            // 添加新字段
                            section_obj.insert("SectionIsOpen".to_string(), json!(false));
                            // 插入一个数组openTeam
                            section_obj.insert("sectionOpenTeam".to_string(), json!([]));
                        }
                    }
                }
            }
        }
    }   
    // 将课程信息解析为UploadCourse格式
    let course_info_ext = UploadCourse {
        id: course_info["id"].as_i64().map(|v| v as i32),
        course_name: course_info["courseName"].as_str().unwrap_or("").to_string(),
        course_slug: course_info["courseSlug"].as_str().unwrap_or("").to_string(),
        course_description: course_info["courseDescription"].as_str().map(String::from),
        publish: course_info["publish"].as_i64().unwrap_or(0) as i32,
        statist: course_info["statist"].as_object().map(|v| serde_json::Value::Object(v.clone())),
        indics: Some(indics.clone()),
        container_info: course_info["containerInfo"].as_object().map(|v| serde_json::Value::Object(v.clone())),
        creater: course_info["creater"].as_object().map(|v| serde_json::Value::Object(v.clone())),
        course_type: course_info["courseType"].as_str().unwrap_or("").to_string(),
        save_code: course_info["saveCode"].as_i64().unwrap_or(1) as i32,
        save_run_result: course_info["saveRunResult"].as_i64().unwrap_or(1) as i32,
        allow_paste: course_info["allowPaste"].as_i64().unwrap_or(1) as i32,
        allow_copy: course_info["allowCopy"].as_i64().unwrap_or(1) as i32,
        question_answer: course_info["questionAnswer"].as_i64().unwrap_or(0) as i32,
        program_language: course_info["programLanguage"].as_str().unwrap_or("").to_string(),
        teams: course_info["teams"].as_object().map(|v| serde_json::Value::Object(v.clone())),
        history_teams: course_info["historyTeams"].as_object().map(|v| serde_json::Value::Object(v.clone())),
        teachers: course_info["teachers"].as_object().map(|v| serde_json::Value::Object(v.clone())),
        upload_count: course_info["uploadCount"].as_i64().unwrap_or(0) as i32,
        download_count: course_info["downloadCount"].as_i64().unwrap_or(0) as i32,
        created_at: serde_json::from_value::<NaiveDateTime>(course_info["created_at"].clone()).unwrap_or(start_time.naive_local()),
        updated_at: serde_json::from_value::<NaiveDateTime>(course_info["updated_at"].clone()).unwrap_or(start_time.naive_local()),
        deleted_at: None,
    };
    let mut course = Vec::new();
    course.push(course_info_ext);

    // 章节信息
    // let sections_info: Vec<Value> = serde_json::from_value(indics[0]["sections"].clone()).unwrap();
    let mut sections: Vec<SectionItem> = Vec::new();
    let indics:Vec<Value> = serde_json::from_value(indics).unwrap();
    for chapter in indics {
        let sections_info: Vec<Value> = serde_json::from_value(chapter["sections"].clone()).unwrap();
        for section in sections_info {
            let section_info = SectionItem {
                id: section["sectionID"].as_i64().unwrap() as i32,
                course_id: course_info["courseSlug"].as_str().unwrap().to_string(),
                chapter_name: chapter["chapterName"].as_str().unwrap().to_string(),
                section_name: section["sectionName"].as_str().unwrap().to_string(),
                section_type: section["sectionType"].as_str().unwrap().to_string(),
                ext: section["ext"].as_str().unwrap().to_string(),
                created_at: start_time.naive_utc(),
            };
            sections.push(section_info);
        }
    }

    // 开启事务
     let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(_) => return false
    };
    let mut rollback = false;
    // 填入新增课程信息
    match Mutation::course_save(&txn, &course, false ,&start_time.naive_utc()).await  {
        Ok(r) => r,
        Err(_) => {
            rollback = true;
            false
        }
    };
    // let res = Mutation::course_save(&txn, &course, true ,&start_time.naive_utc()).await;
    if rollback {
        let _ = txn.rollback().await;
        return false;
    }
    // 插入新增章节信息
    match Mutation::section_save(&txn, &sections, false, &start_time.naive_utc()).await  {
        Ok(r) => r,
        Err(_) => {
            rollback = true;
            false
        }
   };
   if rollback {
        let _ = txn.rollback().await;
        return false;
    }
//    let res = txn.rollback().await;
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(_) => return false
    };
    
    true

}

fn get_course(slug:String) -> Value  {

    //获取对应course文件路径
    let course_slug = slug ;
    let course_path = get_path_in_exe_dir("course");
    let course_path = course_path.to_str().unwrap();
    let path = format!("{}/{}/course.json", course_path, course_slug);

    //按照对应地址读取文件
    let file_content = fs::read_to_string(path).expect("加载课程目录文件失败");

    //获取文件中的JSON数据
    let course = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
    
    // 返回 JSON 数据
    course
}
#[get("/web/getFileDir/{courseSlug}")]
async fn get_file_dir(
    session: Session,
    app_state: web::Data<AppState>,
    course_slug: web::Path<String>
) -> Result<HttpResponse, BusinessError> {
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();
    let path_dir = get_path_in_exe_dir("course");
    
    BusinessResponse::ok(true).to_json_result()

}

// 更改章开放权限及开放班级
#[derive(Deserialize)]
struct UpdateCourseChapterRule {
    course_slug: String,
    chapter_id: Vec<i32>,
    open_team: Vec<i32>,
    option: String,
    switch_chapter_is_open: i32
}

#[post("/api/course/Chapter/rule")]
async fn update_course_chapter_rule(
    app_state: web::Data<AppState>,
    params: web::Json<UpdateCourseChapterRule>
) -> Result<HttpResponse, BusinessError> {
    let start_time = Utc::now();
    // 加载Session
    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    // 查找课程信息
    // println!("{}", params.course_slug);
    let indics_record = match Query::query_indics_by_slug(&txn, params.course_slug.clone()).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("使用slug加载课程信息时失败 {}", e.to_string()).to_string() })
        }
    };
    // 根据选择方式更改课程信息
    let indics_record: Value =Some(indics_record.clone()).into();
    let mut indics_info: Value = indics_record["indics"].clone();
    // println!("{}", indics_info);
    match params.option.as_str() {
        "currentClassAndAllChapter" => {
            // for indics in &mut indics_info {
            //     if let Value::Array(open_team) = indics["open_team"] {
            //         if open_team.is_empty() {
            //             open_team = Vec::new();
            //         }
            //     }

            //     if indics["batchSwitchChapterType"] == 1 {
            //         let mut new_open_team = HashSet::from_iter(indic.open_team.iter().cloned());
            //         new_open_team.insert(class_id);
            //         indic.open_team = new_open_team.into_iter().collect();
            //     } else {
            //         indic.open_team.retain(|&t| t != class_id);
            //     }
            // }
        }
        "AllClassAndCurrentChapter" => {
            if let Value::Array(chapter) = &mut indics_info {
                let mut i = 0;
                for chapter in chapter {
                    if let Value::Object(chapter_obj) = chapter {
                        if chapter_obj.get("chapterIndex").unwrap() == &params.chapter_id[i] {}
                        // 添加新字段
                        chapter_obj.insert("ChapterIsOpen".to_string(),params.switch_chapter_is_open.into() );
                        // 插入一个数组openTeam
                        chapter_obj.insert("chapterOpenTeam".to_string(), params.open_team.clone().into() );
                        
                    }
                    i += 1;
                }
            }
        }
        "AllClassAndAllChapter" => {
            // for indic in &mut current_course.indics {
            //     if switch_type == "open" {
            //         indic.open_team = current_course.teams.clone();
            //     } else {
            //         indic.open_team.clear();
            //     }
            // }
        }
        _ => panic!("Invalid option"),
    }
    // println!("{}", indics_info);
    
    

    let indics_res = match Mutation::indics_save(&txn, Some(indics_info), params.course_slug.clone(), &start_time.naive_utc()).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("更新课程信息失败 {}", e.to_string()).to_string() })
        }
    };
    
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    BusinessResponse::ok(indics_res).to_json_result()
}



// UDP实现下载课程文件
#[get("/api/web/getCourseFiles/{course_slug}")]
pub async fn get_course_files(
    slug: web::Path<String>,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError> {
    let mut udp_file_server = app_state.udp_file_server.clone();
    let mut udp_server = app_state.udp_server.clone();
    // 检测课程在教师本地是否存在
    let (file_create_result , file_id) = udp_file_server.send_course_file(slug.to_string(), 1, vec![]).await;
    // 传输文件哈希值
    match file_create_result {
        TaskState::Exist | TaskState::Create => {

            let conn = &app_state.conn;

            // 开启事务
            let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
                Ok(r) => r,
                Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
            };
            
            let current_course_slug = match Query::system_config_find_by_key(&txn, "current_course_slug").await {
                Ok(r) => r,
                Err(e) => {
                    txn.rollback().await.unwrap();
                    return Err(BusinessError::InternalError { reason: format!("自系统配置内获取当前训练计划ID失败 {}", e.to_string()) });
                }
            };
            if let Some(current_course_slug) = current_course_slug {
                // println!("{} {}", current_course_slug, slug.to_string());
                if current_course_slug == slug.to_string() {
                    // println!("{}", current_course_slug);
                    let mut current_self = udp_server.clone();
        
                    current_self.this_udp.message_type = UDPMessageType::StartClass;
                    current_self.this_udp.message = Some(slug.clone());
            
                    // 检查对应目录是否存在
                    let dir_path = get_path_in_exe_dir("course").join(format!("{}.zip", slug.clone()));
                    if !dir_path.exists() {
                        return Err(BusinessError::InternalError { reason: format!("对应目录不存在") });
                    }
                    // 将文件作为二进制流读出
                    let file_content = fs::read(&dir_path).unwrap();
                    // 读取文件id（sha256哈希值）
                    let hash = digest::digest(&digest::SHA256, &file_content);
                    let hash_bytes: &[u8] = hash.as_ref();
                    current_self.this_udp.file_id = hash_bytes.to_vec();
            
                    let announce_msg = serde_json::to_string(&current_self.this_udp).unwrap();
                    UDPService::announce( 
                        &current_self.socket,
                        announce_msg.as_str(),
                        (current_self.multicast_addr, current_self.multicast_port),
                    )
                    .await; 
                }
            }
            
            // 结束事务
            match txn.commit().await {
                Ok(r) => r,
                Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
            };
            
            // 向当前学生终端广播训练计划更新消息
            let websocket_server = &app_state.ws_server;
            let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: r#"{"type":"course/create"}"#.into() };
            let _resp = websocket_server.send(msg).await;
            return BusinessResponse::ok(file_id).to_json_result();        
        }
        TaskState::Failed => {
            return Err(BusinessError::InternalError { reason: format!("任务创建失败 ").to_string() })
        }
    }    

}
// 重传课程文件
#[derive(Deserialize)]
struct CourseFilesReset {
    part_ids: Vec<u16>,
}
// 重传课程文件
#[post("/api/web/courseFilesReset/{course_slug}")]
pub async fn course_files_reset(
    slug: web::Path<String>,
    param: web::Json<CourseFilesReset>,
    app_state: web::Data<AppState>
) -> Result<HttpResponse, BusinessError> {

    let part_ids = param.part_ids.clone();

    let mut udp_file_server = app_state.udp_file_server.clone();
    let mut udp_server = app_state.udp_server.clone();
    // 检测课程在教师本地是否存在
    let (file_create_result , file_id) = udp_file_server.send_course_file(slug.to_string(), 1, part_ids).await;
    // 传输文件哈希值
    match file_create_result {
        TaskState::Exist | TaskState::Create => {
            return BusinessResponse::ok(file_id).to_json_result();        
        }
        TaskState::Failed => {
            return Err(BusinessError::InternalError { reason: format!("任务创建失败 ").to_string() })
        }
    }    

}

// 开始上课计划（tcp实现，待优化）
#[derive(Debug, Clone, PartialEq, Eq, Deserialize, Serialize)]
#[serde(rename_all = "camelCase")]
struct StartCourse{
    course_slug:String
}
#[post("/api/admin/teacher/course/plan/start")]
pub async fn post_local_course(session: Session, request: web::Json<StartCourse>, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    let course_slug = request.course_slug.clone();
    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };
    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let conn = &app_state.conn;
    let now = Local::now().naive_local();


    // 检查对应目录是否存在
    let dir_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug));
    if !dir_path.exists() {
        return Err(BusinessError::ProcessError { reason: format!("文件{}不存在" , course_slug)})
    }


    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };


    // 查询是否有未结束课程计划，如果有则禁止创建新的课程计划
    // 自系统配置表读取当前课程ID
    let current_train_plan_id = match Query::system_config_find_by_key(&txn, "current_course_slug").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("自系统配置内获取当前课程ID失败 {}", e.to_string()).to_string() });
        }
    };


    if current_train_plan_id.is_some() {
        txn.rollback().await.unwrap();
        return Err(BusinessError::ProcessError { reason: "请先结束之前的课程计划，再行启动新计划".to_string() });
    }


    // 写入当前课程到系统配置表
    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
    key_value_map.insert("current_course_slug".into(), Some(course_slug.to_string()));


    match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
        Ok(count) => count,
        Err(e) => return Err(BusinessError::ProcessError { reason: e.to_string() })
    };


    // 清空当前训练计划学生的登录ip
    match Mutation::clear_current_user_login_ip(&txn).await  {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("清空当前学生的登录ip失败 {}", e.to_string()).to_string() })
    };


    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };


    // 向当前学生终端广播训练计划更新消息
    let websocket_server = &app_state.ws_server;
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: r#"{"type":"course/create"}"#.into() };
    let _resp = websocket_server.send(msg).await;
    
    // 返回成功
    BusinessResponse::ok(course_slug).to_json_result()
}
// 重置上课计划（tcp实现，待优化）
#[put("/api/admin/teacher/course/plan/reset")]
pub async fn put_local_course_reset(session: Session, app_state: web::Data<AppState>) -> Result<HttpResponse, BusinessError> {
    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let conn = &app_state.conn;
    let now = Local::now().naive_local();

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 将当前训练计划自当前训练计划去除
    // 写入当前训练计划到系统配置表
    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
    key_value_map.insert("current_course_slug".into(), None);

    // 从system_config中读取配置
    let course_slug = match Query::system_config_find_by_key(&txn, "current_course_slug").await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::ProcessError { reason: format!("自系统配置内获取当前配置失败 {}", e.to_string()).to_string() });
        }
    };
    if course_slug.is_none() {
        return Err(BusinessError::ProcessError { reason: "当前没有正在进行中的课程".to_string() });
    }
    let course_slug = course_slug.unwrap();

    match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
        Ok(count) => count,
        Err(e) => return Err(BusinessError::ProcessError { reason: e.to_string() })
    };

    // 重置时清空终端ip绑定信息
    // match Mutation::clear_terminals_user(&txn).await {
    //     Ok(count) => count,
    //     Err(e) => return Err(BusinessError::ProcessError { reason: e.to_string() })
    // };

    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    // 向当前学生终端广播训练中状态
    let websocket_server = &app_state.ws_server;
    let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: r#"{"type":"course/reset"}"#.into() };
    let _resp = websocket_server.send(msg).await;

    let process= serde_json::json!({
        "type": "ClassStop"
    }).to_string();

    let mut udp = app_state.udp_server.clone();

    // 设置当前设备的公告标志为 false
    udp.this_udp.message_type = UDPMessageType::Setting;
    udp.this_udp.message = Some(process);
    let announce_msg = serde_json::to_string(&udp.this_udp).unwrap();

    let udp_file_server = app_state.udp_file_server.clone();

    // 清除任务
    // let course_slug = udp_response.message.clone().unwrap();



    // 发送公告消息
    UDPService::announce(
        &udp.socket,
        announce_msg.as_str(),
        (udp.multicast_addr, udp.multicast_port),
    )
    .await;

    udp.this_udp.message_type = UDPMessageType::StopClass;
    let process= course_slug.clone();
    udp.this_udp.message = Some(process);
    let announce_msg = serde_json::to_string(&udp.this_udp).unwrap();
    // 发送公告消息
    UDPService::announce(
        &udp.socket,
        announce_msg.as_str(),
        (udp.multicast_addr, udp.multicast_port),
    )
    .await;

    // 设置当前设备的公告标志为 false
    udp.this_udp.message_type = UDPMessageType::Stop;

    // 延时一秒
    tokio::time::sleep(std::time::Duration::from_secs(1)).await;

    // 获取 Mutex 的锁
    let mut tasks_guard = udp_file_server.tasks.lock().await;

    // 使用 retain 方法删除匹配的任务
    tasks_guard.retain(|task| task.course_slug != course_slug);
    // 释放tasks的锁
    drop(tasks_guard);

    // 返回成功
    BusinessResponse::ok(true).to_json_result()
}
// 获取当前课程计划
#[get("/api/admin/teacher/get/course/config")]
pub async fn get_course_by_config(
    app_state: web::Data<AppState>,
) -> Result<HttpResponse, BusinessError> {
    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };


    let key_value = match Query::system_config_find_by_key(&txn, &"current_course_slug").await {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::ProcessError { reason: format!("自系统配置内获取配置失败 {}", e.to_string()).to_string() });
        }
    };
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::ProcessError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };

    let course_slug = match key_value {
        Some(r) => r,
        None => {
            return Err(BusinessError::ProcessError { reason: format!("未开始上课")});
            "".to_string()
        },
    };
    // 获取文件hash值
    // 检查对应目录是否存在
    let dir_path = get_path_in_exe_dir("course").join(format!("{}.zip", course_slug));
    if !dir_path.exists() {
        return Err(BusinessError::ProcessError { reason: format!("文件{}不存在" , course_slug)})
    }
    // 将文件作为二进制流读出
    let file_content = fs::read(&dir_path).unwrap();
    // 创建文件id（sha256哈希值）
    let hash = digest::digest(&digest::SHA256, &file_content);
    let hash_bytes: &[u8] = hash.as_ref();
    
    let response = json! ({
        "courseSlug": course_slug,
        "fileHash": hex::encode(hash_bytes),
    });

    // 反馈
    BusinessResponse::ok(response).to_json_result()
}
// 下载课程(http实现)
#[get("/api/student/get/course/file/content/{slug}")]
pub async fn get_file_content(slug: web::Path<String>)-> Result<HttpResponse, BusinessError>{
    let dir_path = get_path_in_exe_dir("course").join(format!("{}.zip", slug));
    if !dir_path.exists() {
        return Err(BusinessError::ProcessError { reason: format!("文件{}不存在" , slug)})
    }
    // 将文件作为二进制流读出
    let file_content = fs::read(&dir_path).unwrap();
    BusinessResponse::ok(file_content).to_json_result()
}

// 使用idle运行代码
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct RunParams {
    source: String,
    chapter_name: String,
    course_slug: String,
    section_name: String,
}
#[post("/api/course/code/run")]
pub async fn code_run(session: Session, request: web::Json<RunParams>) -> Result<HttpResponse, BusinessError> {    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    }
    let user_id = user_id.unwrap();
    let course_slug = request.course_slug.clone();
    let chapter_name = request.chapter_name.clone();
    let section_name = request.section_name.clone();
    let source = request.source.clone();
    // 寻找pyhton路径
    // 检查系统
    let os_version = match get_system_version(){
        Ok(os_version) => os_version,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("不支持的操作系统版本{} ", e)}),
    };
    // let mut python_path = get_path_in_exe_dir("../ipython").join("python-3.9.22-embed-amd64");
    let mut python_path = get_path_in_exe_dir("../ipython").join("python-3.8.20-embed-amd64");
    // if os_version == "win7"{
    //     python_path = get_path_in_exe_dir("../ipython").join("python-3.8.20-embed-amd64");
    // }
    // 如果该路径存在
    // if Path::new(&python_path.join("win10")).exists() {
    if !python_path.exists() {
        return Err(BusinessError::AccountError { reason: "找不到python环境".to_string() });
    }
    let code_path = get_path_in_exe_dir("tmp").join("student").join(course_slug).join(chapter_name).join(section_name).join(user_id.to_string());
    if !code_path.exists() {
        let _ = fs::create_dir_all(code_path.clone());
    }
    let code_path = code_path.join("tmp_code.py");
    let _ = tokio::fs::write(code_path.clone(), source).await;
    // let idlelib
    //将路径中的\\替换为/
    let python_path = python_path.to_str().unwrap().replace( "\\\\" ,"/" );
    let code_path = code_path.to_str().unwrap().replace(  "\\\\","/" );
    let _output = Command::new("cmd")
        .current_dir(python_path)
        .arg("/C")
        // .arg(format!("{}/python.exe", python_path))
        .arg(".\\python")
        .arg("-m")
        .arg("idlelib")
        .arg("-r")
        .arg(code_path)
        .spawn()
        .expect("Failed to execute command");
    // let mut child = tokio::process::Command::new("cmd")
    //     .current_dir(python_path)
    //     .arg("/C")
    //     // .arg(format!("{}/python.exe", python_path))
    //     .arg(".\\python")
    //     .arg(code_path)
    //     .stdout(Stdio::piped())
    //     .stderr(Stdio::piped())
    //     .spawn()
    //     .expect("Failed to execute command");

    // let output = child.wait_with_output().await.unwrap();
    // let stdout = String::from_utf8_lossy(&output.stdout);
    // println!("output.status = {:?}, stdout = {:?}", output.status, stdout);


    // 返回成功
    BusinessResponse::ok(true).to_json_result()
}
#[get("/api/user/session")]
async fn get_user_session(
    session: Session,
    app_state: web::Data<AppState>,
    // params: web::Query<QuerySectionContentAndType>
) -> Result<HttpResponse, BusinessError> {
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };
    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    } 
    let user_id = user_id.unwrap();

    let conn = &app_state.conn;

    // let user_admin_authority = match session.get::<Value>("user_admin_authority") {
    //     Ok(r) => r,
    //     Err(_) => None,
    // };
    // let user_display_name = match session.get::<Value>("user_display_name") {
    //     Ok(r) => r,
    //     Err(_) => None,
    // };

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    // 读取账号是否存在
    let user_record = match Query::user_find_by_id(&txn, user_id).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
        }
    };
    
    let user_admin_authority = user_record.unwrap().admin_authority;
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };

    let mut is_admin = true;
    if user_admin_authority.is_none() {
        is_admin = false;
    }
    // let mut session_value = serde_json::to_value(session).unwrap();
    // 在session末尾添加is_admin字段
    // session_value["is_admin"] = Value::Bool(is_admin);
    let res = json!({
        // "session": session_value,
        "userID": user_id,
        "isAdmin": is_admin,
        // "user_admin_authority": user_admin_authority,
        // "user_display_name": user_display_name
    });

    BusinessResponse::ok(res).to_json_result()
}
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct StatisResult {
    course_slug: String,
    chapter_name: String,
    section_name: String,
    team_i_d: Option<i32>
}
#[get("/api/admin/getStatisResult")]
async fn get_statis_result(
    session: Session,
    app_state: web::Data<AppState>,
    params: web::Query<StatisResult>
) -> Result<HttpResponse, BusinessError> {
    // 加载Session获取用户ID
    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先登录本地站点".to_string() })
    };
    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先账号登录本地站点".to_string() });
    } 
    let user_id = user_id.unwrap();

    // let user_admin_authority = match session.get::<Value>("user_admin_authority") {
    //     Ok(r) => r,
    //     Err(_) => None,
    // };

    let mut is_admin = true;
    // if user_admin_authority.is_none() {
    //     is_admin = false;
    // }
    
    let course_slug = params.course_slug.clone();
    let chapter = params.chapter_name.clone();
    let section = params.section_name.clone();
    let if_load_code = true;
    let team_id = params.team_i_d.clone();

    // 获取对应course文件路径
    let course_path = get_path_in_exe_dir("course");
    let course_path = course_path.join(course_slug.clone()).join("course.json");

    // submitRecord
    let submit_record = json!({});
    
    // 解析目录文件，拼接对应字段
    let mut chapter_name = String::new();
    let mut section_name = String::new();
    let mut section_type = String::new();
    let mut section_id = 0;
    
    let file_content = fs::read_to_string(course_path).expect("加载课程目录文件失败");
    let course_content: Value = serde_json::from_str(&file_content).unwrap();
    let mut ext = String::new();
    let mut status = None;

    //遍历course_content中的indics字段，获取必要信息
    for value in course_content["indics"].as_array().unwrap() {
        // 获取chapter_name
        if value["chapterName"] == chapter {
            chapter_name = format!("{}.{}", value["chapterIndex"],value["chapterName"]).replace("\"", "");
            // 获取section_name
            for section_value in value["sections"].as_array().unwrap() {
                if section_value["sectionName"] == section {
                    section_name = format!("{}.{}", section_value["sectionIndex"],section_value["sectionName"]).replace("\"", "");
                    section_id = section_value["sectionID"].as_i64().unwrap() as i32;
                    ext = section_value["ext"].as_str().unwrap().to_string();
                    section_type = section_value["sectionType"].as_str().unwrap().to_string();
                    status = section_value["status"].as_bool();
                }
            }
        }
    }
    // 获取课程内容文件
    let content_path = get_path_in_exe_dir("course");
    let content_path = content_path.to_str().unwrap();
    let path = format!("{}/{}/{}/{}.{}", content_path,course_slug , chapter_name , section_name , ext);
    
    let file_info = fs::metadata(path).unwrap();
    let mtime = file_info.modified().unwrap();
    let mtime_ms = mtime.duration_since(UNIX_EPOCH).unwrap().as_secs_f64() * 1_000.0;
    let mtime_datetime = DateTime::<Utc>::from(mtime).format("%Y-%m-%dT%H:%M:%S%.3fZ").to_string();
    let stat = json!({
        "mtime": mtime_datetime,
        "mtime_ms": mtime_ms,        
    });

    


    // content
    let content_path = get_path_in_exe_dir("course");
    let content_path = content_path.to_str().unwrap();
    let content_path = format!("{}/{}/{}/{}.{}", content_path,course_slug , chapter_name , section_name , ext);

    // 按照对应地址读取文件
    let mut course_content: Value = serde_json::Value::Array(Vec::new());
    // 获取文件中的JSON数据
    match section_type.clone().as_str() {
        "OJ" => {
            let file_content = fs::read_to_string(content_path).expect("加载课程目录文件失败");
            let mut content = serde_json::from_str::<OJContent>(&file_content).unwrap();
            for test_file in &mut content.judge_menu.test_files {
                if test_file.r#type == "inout" {
                    let test_count = test_file.file_name.clone();
                    // 创建目录
                    let file_path = get_path_in_exe_dir("data").join(course_slug.clone()).join(chapter.clone()).join(section.clone());
                    let _ = create_dir_all(&file_path).await;
                    // 写入input文件
                    let input_file_path = file_path.join(format!("{}.in", test_count.to_string()));
                    let mut input_file = File::create(input_file_path.clone()).expect("Unable to create file");
                    // let input_info = fs::read_to_string(test_file.r#in.clone()).expect("加载课程目录文件失败");
                    input_file.write_all(test_file.r#in.clone().as_bytes()).expect("Unable to write data");
                    // 写入output文件
                    let output_file_path = file_path.join(format!("{}.out", test_count.to_string()));
                    let mut output_file = File::create(output_file_path.clone()).expect("Unable to create file");
                    // let output_info = fs::read_to_string(test_file.out.clone()).expect("加载课程目录文件失败");
                    output_file.write_all(test_file.out.clone().as_bytes()).expect("Unable to write data");
                    // 修改格式
                    let input_file_path = input_file_path.to_str().unwrap().to_string();
                    let output_file_path = output_file_path.to_str().unwrap().to_string();
                    test_file.r#in = input_file_path;
                    test_file.out = output_file_path;
                }
            }                
                          
        
            let oj = OJData {
                r#type: "OJ".to_string(),
                content,
                stat: HashMap::new(),
                status: true,
            };

            course_content = serde_json::to_value(oj).unwrap();
        }
        "PPT" => {
            let file_content = fs::read_to_string(content_path).expect("加载课程目录文件失败");
            let mut ppt_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
            // 插入课程类型
            match ppt_content {
                Value::Object(ref mut object) => {
                    // 如果 train_course 是一个对象，直接添加新属性    
                    object.insert("type".to_string(), serde_json::Value::String(section_type.clone()));
                }
                _ => {
                    
                }
            }
            course_content = json!({
                "type": section_type,
                "content": ppt_content
            });

        },
        _ => {
            let file_content = fs::read_to_string(content_path).expect("加载课程目录文件失败");
            course_content = serde_json::from_str::<Value>(&file_content).expect("解析课程目录文件失败");
            // 插入课程类型
            match course_content {
                Value::Object(ref mut object) => {
                    // 如果 train_course 是一个对象，直接添加新属性
                    object.insert("type".to_string(), serde_json::Value::String(section_type.clone()));
                }
                _ => {
                    
                }
            }
        }
    }
    
    // 
    let conn = &app_state.conn;
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };
    

    // 读取账号是否存在
    let user_record = match Query::user_find_by_id(&txn, user_id).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("使用用户名加载用户时失败 {}", e.to_string()).to_string() })
        }
    };
    
    let user_admin_authority = user_record.unwrap().admin_authority;

    // let mut is_admin = true;
    if user_admin_authority.is_none() {
        is_admin = false;
    }
    let course_info_item = match Query::course_info_find_by_slug_json(&txn, course_slug.clone()).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("查找课程信息失败 {}", e.to_string()).to_string() })
        }
    };

    let mut course_id ;
    let mut save_code ;
    let mut save_run_result ;
    let mut container_info = None;

    if let Some(course_info_item) = course_info_item {
        course_id = course_info_item.id;
        save_code = course_info_item.save_code;
        save_run_result = course_info_item.save_run_result;
        container_info = course_info_item.container_info;
    } else {
        txn.rollback().await.unwrap();
        return Err(BusinessError::InternalError { reason: "查找课程信息失败".to_string() })
    }    
    

    let teams = match Query::all_team_find(&txn).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("查找班级失败 {}", e.to_string()).to_string() })
        }
    };

    // record recordTime

    let user_record = match Query::user_record_find_by_section_id_and_user_id(&txn, user_id.clone(), section_id).await  {
        Ok(r) => r,
        Err(e) => {
            txn.rollback().await.unwrap();
            return Err(BusinessError::InternalError { reason: format!("查找记录失败{}", e.to_string()).to_string() })
        }       
    };
    let mut record = Option::None;
    // let mut created_at;
    // let mut updated_at;
    let mut record_time = Value::Null;
    let mut total_score = 0;
    let mut pass_count = 0;
    if let Some(user_record) = user_record {
        record = user_record.record;
        let record_times_json = json!({
            "create": user_record.created_at,
            "update": user_record.updated_at
        });
        record_time = record_times_json;
        total_score = user_record.total_score;
        pass_count = user_record.pass_count;
    }



    let mut record_res: HashMap<String, Value> = HashMap::new();
    let mut record_message = json!({});
    match section_type.as_str() {
        "AI" => {
            if let Some(record) = record.clone() {
                let record_map: HashMap<String, Value> = serde_json::from_value(record).unwrap();

                for (uuid, source_params) in record_map {
                    let sourse_type = source_params["microAppFileType"]
                    .as_str()
                    .unwrap_or_else(|| {
                        record_res.insert(uuid.clone(), source_params.clone());
                        ""
                    });
                    let full_ext = match sourse_type {
                        "text" => {
                            "txt"
                        },
                        "table" => {
                            "csv"
                        },
                        "mind" | "flow" | "networksimulator" | "spreadsheet" | "drawio" | "Scratch" | "MicroBit" => {
                            "json"
                        }
                        _ => {
                            continue;
                        }
                    };
                    let full_name = format!("{}.{}", uuid, full_ext);
                    let file_path = get_path_in_exe_dir("static").join("student").join("course").join(course_slug.clone()).join(user_id.to_string()).join(chapter.clone()).join(full_name.clone());
                    if file_path.exists() {
                        let file_content = fs::read_to_string(file_path.clone()).expect("加载课程记录文件失败");
                        
                        let mut message;
                        if sourse_type == "table" {
                            let cells: Vec<Value> = serde_json::from_value(course_content["cells"].clone()).unwrap();
                            let mut cell_info: Vec<Vec<String>> =Vec::new();
                            for cell in cells {
                                let cell_uuid = cell["metadata"]["UUID"].as_str().unwrap();
                                if cell_uuid == uuid {
                                    cell_info = serde_json::from_value(cell["metadata"]["config"]["cell"].clone()).unwrap();
                                }
                            }

                            let file_content = serde_json::to_vec(&file_content).unwrap();
                            let file_content = match str::from_utf8(&file_content) {
                                Ok(v) => v.trim_matches('"').to_string(),
                                Err(e) => panic!("Invalid UTF-8 sequence: {}", e),
                            };
                            let file_content: Vec<String> = file_content.split("\\n").map(String::from).collect();
                            if !file_content.is_empty() {
                                let result = file_content
                                    .iter()
                                    .enumerate()
                                    .map(|(i, data)| {
                                        data.split(',')
                                            .enumerate()
                                            .map(|(j, v)| json!({
                                                "readOnly": if i < cell_info.len() && j < cell_info[i].len() {
                                                    true
                                                } else {
                                                    false
                                                },
                                                "style": {"background": "rgb(238, 238, 238)"},
                                                "value": v,
                                            }))
                                            .collect::<Vec<Value>>()
                                    })
                                    .collect::<Vec<Vec<Value>>>();
                        
                                message = json!(result);
                            } else {
                                message = json!([]);
                            }
                        }else {
                            message = serde_json::from_str(&file_content).expect("解析课程记录文件失败");
                        }
                        
                        let code_result: Value = json!({
                            "fileNanme": uuid,
                            "message": message,
                            //source_params
                            "type": sourse_type
                        });
                        let mut code_result_vec: Vec<Value> =Vec::new();
                        code_result_vec.push(code_result);
                        let turtle_models: Vec<Value> = Vec::new();
                        let result = json!({
                            "codeResult": code_result_vec,
                            "turtleModels": turtle_models
                        });
                        let file_info = fs::metadata(file_path).unwrap();
                        let mtime = file_info.modified().unwrap();
                        let mtime_ms = mtime.duration_since(UNIX_EPOCH).unwrap().as_secs_f64() * 1_000.0;
                        let mtime_datetime = DateTime::<Utc>::from(mtime).format("%Y-%m-%dT%H:%M:%S%.3fZ").to_string();
                        let stat = json!({
                            "mtime": mtime_datetime,
                            "mtime_ms": mtime_ms,        
                        });
                        let code_result = json!({
                            "fileStat": stat,
                            "fullName": full_name,
                            "result": result,
                            "status": "运行通过",
                            "type": "success"
                        });
                        record_res.insert(uuid.clone(), code_result);
                    }
                }
                record_message = serde_json::to_value(record_res).unwrap();
            } 
        }
        "OI" => {
            if let Some(record) = record.clone() {
                let mut record_map: HashMap<String, Value> = serde_json::from_value(record).unwrap();
                record_map.insert("totalScore".to_string(), json!(total_score) );
                record_map.insert("passCount".to_string(), json!(pass_count) );
                record_message = serde_json::to_value(record_map).unwrap();
            }
        },
        "OJ" | "CodeBlank" | "PPT" => { 
            if let Some(record) = record.clone() {
                record_message = record;
            }
        },
        "MicroBit" | "Scratch" => { 
            let full_name = format!("{}.json", section);
            let file_path = get_path_in_exe_dir("static").join("student").join("course").join(course_slug.clone()).join(user_id.to_string()).join(chapter.clone()).join(full_name.clone());
            if file_path.exists() {
                let file_content = fs::read_to_string(file_path.clone()).expect("加载课程记录文件失败");
                let message: Value = serde_json::from_str(&file_content).expect("解析课程记录文件失败");
                let result: Value = json!({
                    "fileNanme": section,
                    "message": message,
                });
                let file_info = fs::metadata(file_path).unwrap();
                let mtime = file_info.modified().unwrap();
                let mtime_ms = mtime.duration_since(UNIX_EPOCH).unwrap().as_secs_f64() * 1_000.0;
                let mtime_datetime = DateTime::<Utc>::from(mtime).format("%Y-%m-%dT%H:%M:%S%.3fZ").to_string();
                let stat = json!({
                    "mtime": mtime_datetime,
                    "mtime_ms": mtime_ms,        
                });
                let code_result = json!({
                    "fileStat": stat,
                    "fullName": full_name,
                    "result": result,
                    "status": "运行通过",
                    "type": "success"
                });
                record_message = code_result;

            }
        },
        _ => {
        }
        
    }

    let mut section_records:Vec<SectionRecordItem> = Vec::new();
    let mut student_ids:Vec<i32> = Vec::new();
    // statisResult students
    if let Some(team_id) = team_id {
        (section_records, student_ids) = match Query::section_record_find_by_course_id_and_section_id(&txn, team_id, section_id).await  {
            Ok(r) => r,
            Err(e) => {
                txn.rollback().await.unwrap();
                return Err(BusinessError::InternalError { reason: format!("查找记录失败{}", e.to_string()).to_string() })
            }       
        };
    }
    let mut static_result =  json!({});
    match section_type.as_str() {
        "AI" => {
            let mut question_map: HashMap<String, Value> = HashMap::new();
            if let Some(record) = record.clone() {
                let record_map: HashMap<String, Value> = serde_json::from_value(record).unwrap();
                for (uuid, _) in record_map {
                    let mut error_arr: Vec<i32> = Vec::new();
                    let mut notpass_arr: Vec<i32> = student_ids.clone();
                    let mut pass_arr: Vec<i32> = Vec::new();
                    for section_record in section_records.clone() {
                        if let Some(record_data) = section_record.record {
                            let record_data_map: HashMap<String, Value> = serde_json::from_value(record_data).unwrap();
                            if let Some(record_data_result) = record_data_map.get(&uuid) {
                                if let Some(result) = record_data_result.get("type") {
                                    match result.as_str().unwrap() {
                                        "success" => {
                                            notpass_arr.retain(|&x| x != section_record.user_id);
                                            pass_arr.push(section_record.user_id);
                                        },
                                        "error" => {
                                            notpass_arr.retain(|&x| x != section_record.user_id);
                                            error_arr.push(section_record.user_id);
                                        },
                                        "notRun" => {
                                        },
                                        _ => {
                                            
                                        }
                                    }
                                }
                            }
                        }
                    }
                    let question_res = json!({
                        "error": error_arr,
                        "notPass": notpass_arr,
                        "pass": pass_arr
                    });
                    question_map.insert(uuid, question_res);
                }
                static_result = serde_json::to_value(question_map).unwrap();
            }
            

        }
        "OI" | "CodeBlank" => {
            let mut question_map: HashMap<String, Value> = HashMap::new();
            
            if let Some(record) = record.clone() {
                let record_map: HashMap<String, Value> = serde_json::from_value(record).unwrap();
                for (uuid, _) in record_map {
                    if uuid.contains("submitTimes") {
                        continue;
                    }
                    let mut error_arr: Vec<i32> = Vec::new();
                    let mut notpass_arr: Vec<i32> = student_ids.clone();
                    let mut pass_arr: Vec<i32> = Vec::new();
                    for section_record in section_records.clone() {
                        if let Some(record_data) = section_record.record {
                            let record_data_map: HashMap<String, Value> = serde_json::from_value(record_data).unwrap();
                            if let Some(record_data_result) = record_data_map.get(&uuid) {
                                if let Some(result) = record_data_result.get("status") {
                                    match result.as_bool().unwrap() {
                                        true => {
                                            notpass_arr.retain(|&x| x != section_record.user_id);
                                            pass_arr.push(section_record.user_id);
                                        },
                                        false => {
                                            notpass_arr.retain(|&x| x != section_record.user_id);
                                            error_arr.push(section_record.user_id);
                                        },
                                    }
                                }
                            }
                        }
                    }
                    let question_res = json!({
                        "error": error_arr,
                        "notPass": notpass_arr,
                        "pass": pass_arr
                    });
                    question_map.insert(uuid, question_res);
                }
                static_result = serde_json::to_value(question_map).unwrap();
            } else if team_id.is_some() {
                let student_ids = match Query::user_id_find_by_team_ids(&txn, team_id.unwrap()).await{
                    Ok(ids) => ids,
                    Err(e) => {
                        // static_result = json!({});
                        // return;
                        return Err(BusinessError::ProcessError { reason: format!("班级成员信息有误，请联系管理员 {}", e.to_string()).to_string() })
                    }
                };
                let error_arr: Vec<i32> = Vec::new();
                let notpass_arr: Vec<i32> = student_ids.clone();
                let pass_arr: Vec<i32> = Vec::new();
                if section_type.as_str() == "OI" { 
                    let questions = serde_json::from_value::<Vec<Value>>(course_content["questions"].clone()).unwrap();
                    // 收集questions中每一个questionType不是文本的元素的uuid字段
                    let question_uuids: Vec<String> = questions
                        .iter()
                        .filter(|question| question["questionType"] != "文本")
                        .map(|question| question["UUID"]
                            .as_str()
                            .unwrap()
                            .to_string())
                        .collect();


                    for uuid in question_uuids {
                        let question_res = json!({
                            "error": error_arr,
                            "notPass": notpass_arr,
                            "pass": pass_arr
                        });
                        question_map.insert(uuid, question_res);
                    }
                    static_result = serde_json::to_value(question_map).unwrap();
                } else {
                    let questions = serde_json::from_value::<Vec<Value>>(course_content["questions"].clone()).unwrap();
                    let answers_list = serde_json::from_value::<HashMap<String, Vec<Value>>>(questions[0]["answer"].clone()).unwrap();
                    // 获取所有key
                    let keys = answers_list.keys().cloned().collect::<Vec<_>>();


                    for key in keys {
                        let question_res = json!({
                            "error": error_arr,
                            "notPass": notpass_arr,
                            "pass": pass_arr
                        });
                        question_map.insert(key, question_res);
                    }
                    static_result = serde_json::to_value(question_map).unwrap();
                }

            }
        }
        "OJ" => {
            let mut error_arr: Vec<i32> = Vec::new();
            let mut notpass_arr: Vec<i32> = student_ids.clone();
            let mut pass_arr: Vec<i32> = Vec::new();
            
            for section_record in section_records.clone() {
                if let Some(record_data) = section_record.record {
                    let record_data_map: HashMap<String, Value> = serde_json::from_value(record_data).unwrap();
                    if let Some(result) = record_data_map.get("score") {
                        match result.as_i64().unwrap() {
                            100 => {
                                notpass_arr.retain(|&x| x != section_record.user_id);
                                pass_arr.push(section_record.user_id);
                            },
                            _ => {
                                notpass_arr.retain(|&x| x != section_record.user_id);
                                error_arr.push(section_record.user_id);
                            },
                        }
                    }
                }
            }
            let question_res = json!({
                "error": error_arr,
                "notPass": notpass_arr,
                "pass": pass_arr
            });
            static_result = serde_json::to_value(question_res).unwrap();
        }
        "Scratch" => {
            let judge_steps: std::result::Result<Vec<Value>, serde_json::Error> = serde_json::from_value(course_content["judgeSteps"].clone());
            let judge_steps = judge_steps.unwrap();

            // 获取该班级所有学生id
            if team_id.is_some() {
                let student_ids = match Query::user_id_find_by_team_ids(&txn, team_id.unwrap()).await{
                    Ok(ids) => ids,
                    Err(e) => {
                        // static_result = json!({});
                        // return;
                        return Err(BusinessError::ProcessError { reason: format!("班级成员信息有误，请联系管理员 {}", e.to_string()).to_string() })
                    }
                };
                // 计算代码块，初始化数据
                let mut uuid_list: HashMap<String, HashMap<String, Vec<i32>>> = HashMap::new();
                let mut res_model: HashMap<String, Vec<i32>> = HashMap::new();
                res_model.insert("notPass".to_string(), student_ids);
                res_model.insert("pass".to_string(), Vec::new());
                res_model.insert("error".to_string(), Vec::new());
                for step in judge_steps {
                    let uuid = step["uuid"].as_str().unwrap().to_string();
                    uuid_list.insert(uuid, res_model.clone());
                }
                // 处理用户记录
                for section_record in section_records.clone() {
                    let record_data = section_record.record;
                    let cur_user_id = section_record.user_id;
                    if record_data.is_none() {
                        continue;
                    }
                    let record_data = record_data.unwrap();
                    let judge_steps_record = record_data["judge_steps"].clone();
                    let judge_steps_record: Vec<Value> = serde_json::from_value(judge_steps_record).unwrap();

                    for step_record in judge_steps_record {
                        let uuid = step_record["uuid"].as_str().unwrap().to_string();
                        let pass = step_record
                            .get("pass")
                            .and_then(|v| v.as_bool())
                            .unwrap_or(false); 

                        // 根据pass状态更新对应uuid的统计
                        if let Some(uuid_entry) = uuid_list.get_mut(&uuid) {
                            if pass {
                                // 如果通过，添加到pass列表
                                uuid_entry.get_mut("pass").unwrap().push(cur_user_id);
                                // 并且从not_pass列表中删除
                                uuid_entry.get_mut("notPass").unwrap().retain(|x| *x != cur_user_id);
                            } else {
                                // 如果通过，添加到pass列表
                                uuid_entry.get_mut("error").unwrap().push(cur_user_id);
                                // 并且从not_pass列表中删除
                                uuid_entry.get_mut("notPass").unwrap().retain(|x| *x != cur_user_id);
                            }
                            
                            // // 检查是否有错误状态字段
                            // if let Some(error) = step_record.get("error").and_then(|v| v.as_bool()) {
                            //     if error {
                            //         // 如果有错误，添加到error列表
                            //         uuid_entry.get_mut("error").unwrap().push(cur_user_id);
                            //     }
                            // }
                        }
                    }
                }
                // 将uuid_list转为json
                static_result = json!(uuid_list);                
            } else {
                static_result = json!({});
            }
        }
        "Access" | "Excel" => {
            if team_id.is_some() {
                let student_ids = match Query::user_id_find_by_team_ids(&txn, team_id.unwrap()).await{
                    Ok(ids) => ids,
                    Err(e) => {
                        // static_result = json!({});
                        // return;
                        return Err(BusinessError::ProcessError { reason: format!("班级成员信息有误，请联系管理员 {}", e.to_string()).to_string() })
                    }
                };
                let mut res_model: HashMap<String, Vec<i32>> = HashMap::new();
                res_model.insert("notPass".to_string(), student_ids);
                res_model.insert("pass".to_string(), Vec::new());
                res_model.insert("error".to_string(), Vec::new());

                // 处理用户记录
                for section_record in section_records.clone() {
                    let cur_user_id = section_record.user_id;
                    let record_data = section_record.record;
                    if record_data.is_none() {
                        continue;
                    }
                    let record_data = record_data.unwrap();
                    let steps_record = record_data["steps"].clone();
                    let steps_record: Vec<Value> = serde_json::from_value(steps_record).unwrap();
                    let mut pass = true;
                    for step_record in steps_record {
                        let status = step_record["status"].as_str().unwrap();
                        if status == "error" {
                            pass = false;
                            break;
                        }
                    }
                    if pass {
                        // 如果通过，添加到pass列表
                        res_model.get_mut("pass").unwrap().push(cur_user_id);
                        // 并且从not_pass列表中删除
                        res_model.get_mut("notPass").unwrap().retain(|x| *x != cur_user_id);
                    } else {
                        // 如果未通过，添加到not_pass列表
                        res_model.get_mut("error").unwrap().push(cur_user_id);
                        // 删除pass列表中的数据
                        res_model.get_mut("notPass").unwrap().retain(|x| *x != cur_user_id);
                    }                    
                }
                static_result = json!(res_model);    
            } else {
                static_result = json!({});
            }
        }    
        _ => {
            static_result = json!({});
        }
    }
    


    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
        }
    };

    // 返回结果
    let res = json!({
        "statisResult": static_result,
        "students": student_ids.len(),
    });
    BusinessResponse::ok(res).to_json_result()

}
// 批量删除课程
#[derive(Deserialize,Serialize,Debug,Clone)]
#[serde(rename_all = "camelCase")]
pub struct BatchDeleteCourseParams{
    pub course_slug_list: Vec<String>
}
#[post("/api/admin/course/batchDelete")]
pub async fn batch_delete_course(
    session: Session,
    app_state: web::Data<AppState>,
    params: web::Json<BatchDeleteCourseParams>
) -> Result<HttpResponse, BusinessError> {
    // 加载Session
    let user_admin_authority = match session.get::<Value>("user_admin_authority") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_admin_authority.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let user_id = match session.get::<i32>("user_id") {
        Ok(r) => r,
        Err(_e) => return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() })
    };

    if user_id.is_none() {
        return Err(BusinessError::AccountError { reason: "请先使用教师账号登录本地站点".to_string() });
    }

    let course_slug_list = params.course_slug_list.clone();

    let conn = &app_state.conn;

    // 开启事务
    let txn = match conn.begin_with_config(Some(sea_orm::IsolationLevel::ReadCommitted), Some(sea_orm::AccessMode::ReadWrite)).await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::ProcessError { reason: format!("无法启动数据库事务 {}", e.to_string()).to_string() })
    };

    let current_course_slug = match Query::system_config_find_by_key(&txn, "current_course_slug").await {
        Ok(r) => r,
        Err(e) => {
            return Err(BusinessError::ProcessError { reason: format!("无法从数据库中查询当前课程 {}", e.to_string()).to_string() });
        }
    };
    let mut key_value_map: HashMap<String, Option<String>> = HashMap::new();
    key_value_map.insert("current_course_slug".into(), None);

    if current_course_slug.is_some() { 
        let course_slug = current_course_slug.unwrap();
        let now = Local::now().naive_local();

        match Mutation::system_config_set_by_key_value_map(&txn, key_value_map, &now).await {
            Ok(count) => count,
            Err(e) => return Err(BusinessError::ProcessError { reason: e.to_string() })
        };

        // // 重置时清空终端ip绑定信息
        // match Mutation::clear_terminals_user(&txn).await {
        //     Ok(count) => count,
        //     Err(e) => return Err(BusinessError::ProcessError { reason: e.to_string() })
        // };

        // 向当前学生终端广播训练中状态
        let websocket_server = &app_state.ws_server;
        let msg: ClientMessage = ClientMessage { id: 0, room: String::from("local"), msg: r#"{"type":"course/reset"}"#.into() };
        let _resp = websocket_server.send(msg).await;

        let process= serde_json::json!({
            "type": "ClassStop"
        }).to_string();

        let mut udp = app_state.udp_server.clone();

        // 设置当前设备的公告标志为 false
        udp.this_udp.message_type = UDPMessageType::Setting;
        udp.this_udp.message = Some(process);
        let announce_msg = serde_json::to_string(&udp.this_udp).unwrap();

        let udp_file_server = app_state.udp_file_server.clone();

        // 清除任务
        // let course_slug = udp_response.message.clone().unwrap();



        // 发送公告消息
        UDPService::announce(
            &udp.socket,
            announce_msg.as_str(),
            (udp.multicast_addr, udp.multicast_port),
        )
        .await;

        udp.this_udp.message_type = UDPMessageType::StopClass;
        let process= course_slug.clone();
        udp.this_udp.message = Some(process);
        let announce_msg = serde_json::to_string(&udp.this_udp).unwrap();
        // 发送公告消息
        UDPService::announce(
            &udp.socket,
            announce_msg.as_str(),
            (udp.multicast_addr, udp.multicast_port),
        )
        .await;

        // 设置当前设备的公告标志为 false
        udp.this_udp.message_type = UDPMessageType::Stop;

        // 延时一秒
        tokio::time::sleep(std::time::Duration::from_secs(1)).await;

        // 获取 Mutex 的锁
        let mut tasks_guard = udp_file_server.tasks.lock().await;

        // 使用 retain 方法删除匹配的任务
        tasks_guard.retain(|task| task.course_slug != course_slug);
        // 释放tasks的锁
        drop(tasks_guard);
    }

    // 删除数据库中的课程信息及章节信息
    match Mutation::course_delete_by_slug(&txn, course_slug_list.clone()).await {
        Ok(count) => count,
        Err(e) => return Err(BusinessError::ProcessError { reason: e.to_string() })
    };

    match Mutation::section_delete_by_course_slug(&txn, course_slug_list.clone()).await {
        Ok(count) => count,
        Err(e) => return Err(BusinessError::ProcessError { reason: e.to_string() })
    };
             
    // 结束事务
    match txn.commit().await {
        Ok(r) => r,
        Err(e) => return Err(BusinessError::InternalError { reason: format!("无法提交数据库事务 {}", e.to_string()).to_string() })
    };
    let exe_path = get_path_in_exe_dir("course");
    // 删除对应课程的文件内容
    for course in course_slug_list {
        let course_path =exe_path.join(course.clone());
        let zip_path = exe_path.join(format!("{}.zip", course));
        // 删除课程文件夹及其所有内容
        if course_path.exists() {
            if let Err(e) = fs::remove_dir_all(&course_path) {
                return Err(BusinessError::InternalError { reason: format!("无法删除文件 {}", e.to_string()).to_string() })
            }
        }

        // 删除压缩包文件
        if zip_path.exists() {
            if let Err(e) = fs::remove_file(&zip_path) {
                return Err(BusinessError::InternalError { reason: format!("无法删除文件 {}", e.to_string()).to_string() })
            }            
        }

    }
    BusinessResponse::ok(1).to_json_result()
}


#[get("test")]
pub async fn test() -> Result<HttpResponse, BusinessError> {
    return BusinessResponse::ok(1).to_json_result();
}






    // boa调用js代码示例
    // let mut context = Context::new();
    // let request = match String::from_utf8(request.to_vec()) {
    //     Ok(r) => r,
    //     Err(_e) => return Err(BusinessError::InternalError { reason: "请求数据解析失败".to_string() })
    // };
    // context.register_global_property("request", JsValue::from(request), Attribute::all());
    

    // // 定义一个JavaScript函数
    // let rule_check = r#"
    //     function rule_check(){
    //         const { ctx } = this;

    //         const rule = {
    //             courseSlug: { type: 'string', required: true},
    //             studentRecord: { type: 'object', required: true},
    //             chapterName: { type: 'string', required: true},
    //             sectionName: { type: 'string', required: true},
    //         };

    //         try {
    //             ctx.validate(rule, request);
    //         } catch(e) {
    //             ctx.body = {code: 400, message: e.errors};
    //             return ctx.body;
    //         }

    //         const { courseSlug, studentRecord, chapterName, sectionName, userID, type } = request; 
    //         // ctx.body = {code: 0, message: request.body};
    //         return {courseSlug: courseSlug};

    //     } 
    //     rule_check();
    // "#;

    // // 执行JavaScript代码
    // match context.eval(rule_check) {
    //     Ok(result) => {
    //         // 将结果转换为Rust的类型
    //         if let Some(obj) = result.as_object() {
    //             let sum = obj.get("courseSlug", &mut context).unwrap();


                

    //             // 将 JsValue 转换为字符串
    //             let sum_str = sum.to_string(&mut context).unwrap();

    //         }
    //     }
    //     Err(e) => {
    //     }
    // }