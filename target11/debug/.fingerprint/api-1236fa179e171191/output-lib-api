{"$message_type":"diagnostic","message":"the name `FayeMessageRequest` is defined multiple times","code":{"code":"E0252","explanation":"Two items of the same name cannot be imported without rebinding one of the\nitems under a new local name.\n\nErroneous code example:\n\n```compile_fail,E0252\nuse foo::baz;\nuse bar::baz; // error, do `use bar::baz as quux` instead\n\nfn main() {}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n\nYou can use aliases in order to fix this error. Example:\n\n```\nuse foo::baz as foo_baz;\nuse bar::baz; // ok!\n\nfn main() {}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n\nOr you can reference the item with its parent:\n\n```\nuse bar::baz;\n\nfn main() {\n    let x = foo::baz; // ok!\n}\n\nmod foo {\n    pub struct baz;\n}\n\nmod bar {\n    pub mod baz {}\n}\n```\n"},"level":"error","spans":[{"file_name":"api\\src\\course.rs","byte_start":1636,"byte_end":1667,"line_start":47,"line_end":47,"column_start":5,"column_end":36,"is_primary":true,"text":[{"text":"use crate::faye::FayeMessageRequest;","highlight_start":5,"highlight_end":36}],"label":"`FayeMessageRequest` reimported here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":1045,"byte_end":1069,"line_start":28,"line_end":28,"column_start":5,"column_end":29,"is_primary":false,"text":[{"text":"use faye::FayeMessageRequest;","highlight_start":5,"highlight_end":29}],"label":"previous import of the type `FayeMessageRequest` here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`FayeMessageRequest` must be defined only once in the type namespace of this module","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove unnecessary import","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1632,"byte_end":1670,"line_start":47,"line_end":48,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::faye::FayeMessageRequest;","highlight_start":1,"highlight_end":37},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0252]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the name `FayeMessageRequest` is defined multiple times\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:47:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse faye::FayeMessageRequest;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mprevious import of the type `FayeMessageRequest` here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::faye::FayeMessageRequest;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m`FayeMessageRequest` reimported here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `FayeMessageRequest` must be defined only once in the type namespace of this module\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `console::style`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":178,"byte_end":192,"line_start":6,"line_end":6,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use console::style;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":174,"byte_end":195,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use console::style;","highlight_start":1,"highlight_end":20},{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `console::style`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse console::style;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `FileInfo`, `ServerMessage`, and `TaskState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":219,"byte_end":227,"line_start":7,"line_end":7,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":249,"byte_end":262,"line_start":7,"line_end":7,"column_start":55,"column_end":68,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":55,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":264,"byte_end":273,"line_start":7,"line_end":7,"column_start":70,"column_end":79,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":70,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":219,"byte_end":229,"line_start":7,"line_end":7,"column_start":25,"column_end":35,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":25,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":247,"byte_end":273,"line_start":7,"line_end":7,"column_start":53,"column_end":79,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":53,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":218,"byte_end":219,"line_start":7,"line_end":7,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":273,"byte_end":274,"line_start":7,"line_end":7,"column_start":79,"column_end":80,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":79,"highlight_end":80}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `FileInfo`, `ServerMessage`, and `TaskState`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:7:25\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `log::kv::Value`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":317,"byte_end":331,"line_start":9,"line_end":9,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use log::kv::Value;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":313,"byte_end":334,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use log::kv::Value;","highlight_start":1,"highlight_end":20},{"text":"use rand::Rng;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `log::kv::Value`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::kv::Value;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CopyOptions as CopyDirOption` and `copy as copy_dir`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":589,"byte_end":605,"line_start":17,"line_end":17,"column_start":21,"column_end":37,"is_primary":true,"text":[{"text":"use fs_extra::dir::{copy as copy_dir, CopyOptions as CopyDirOption};","highlight_start":21,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":607,"byte_end":635,"line_start":17,"line_end":17,"column_start":39,"column_end":67,"is_primary":true,"text":[{"text":"use fs_extra::dir::{copy as copy_dir, CopyOptions as CopyDirOption};","highlight_start":39,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":569,"byte_end":639,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use fs_extra::dir::{copy as copy_dir, CopyOptions as CopyDirOption};","highlight_start":1,"highlight_end":69},{"text":"use actix_session::{SessionMiddleware, storage::CookieSessionStore};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `CopyOptions as CopyDirOption` and `copy as copy_dir`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:17:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fs_extra::dir::{copy as copy_dir, CopyOptions as CopyDirOption};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::spawn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":748,"byte_end":760,"line_start":20,"line_end":20,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"use tokio::spawn;","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":744,"byte_end":763,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::spawn;","highlight_start":1,"highlight_end":18},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tokio::spawn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:20:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::spawn;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `runtime`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":777,"byte_end":784,"line_start":22,"line_end":22,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"use tokio::{runtime, sync::mpsc};","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":777,"byte_end":786,"line_start":22,"line_end":22,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"use tokio::{runtime, sync::mpsc};","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":776,"byte_end":777,"line_start":22,"line_end":22,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use tokio::{runtime, sync::mpsc};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":796,"byte_end":797,"line_start":22,"line_end":22,"column_start":32,"column_end":33,"is_primary":true,"text":[{"text":"use tokio::{runtime, sync::mpsc};","highlight_start":32,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `runtime`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:22:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::{runtime, sync::mpsc};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `MultiProgress`, `ProgressBar`, `ProgressState`, and `ProgressStyle`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":816,"byte_end":829,"line_start":23,"line_end":23,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":831,"byte_end":842,"line_start":23,"line_end":23,"column_start":32,"column_end":43,"is_primary":true,"text":[{"text":"use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};","highlight_start":32,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":844,"byte_end":857,"line_start":23,"line_end":23,"column_start":45,"column_end":58,"is_primary":true,"text":[{"text":"use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};","highlight_start":45,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":859,"byte_end":872,"line_start":23,"line_end":23,"column_start":60,"column_end":73,"is_primary":true,"text":[{"text":"use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};","highlight_start":60,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":800,"byte_end":876,"line_start":23,"line_end":24,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};","highlight_start":1,"highlight_end":75},{"text":"use tracing::{debug, info};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `MultiProgress`, `ProgressBar`, `ProgressState`, and `ProgressStyle`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:23:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `debug` and `info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":890,"byte_end":895,"line_start":24,"line_end":24,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{debug, info};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":897,"byte_end":901,"line_start":24,"line_end":24,"column_start":22,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{debug, info};","highlight_start":22,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":876,"byte_end":905,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tracing::{debug, info};","highlight_start":1,"highlight_end":28},{"text":"use dialoguer::{theme::ColorfulTheme, MultiSelect};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `debug` and `info`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:24:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, info};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `MultiSelect` and `theme::ColorfulTheme`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":921,"byte_end":941,"line_start":25,"line_end":25,"column_start":17,"column_end":37,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, MultiSelect};","highlight_start":17,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":943,"byte_end":954,"line_start":25,"line_end":25,"column_start":39,"column_end":50,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, MultiSelect};","highlight_start":39,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":905,"byte_end":958,"line_start":25,"line_end":26,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, MultiSelect};","highlight_start":1,"highlight_end":52},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `MultiSelect` and `theme::ColorfulTheme`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:25:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse dialoguer::{theme::ColorfulTheme, MultiSelect};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `thread`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":1435,"byte_end":1441,"line_start":45,"line_end":45,"column_start":16,"column_end":22,"is_primary":true,"text":[{"text":"use std::{env, thread};","highlight_start":16,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":1433,"byte_end":1441,"line_start":45,"line_end":45,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use std::{env, thread};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":1429,"byte_end":1430,"line_start":45,"line_end":45,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use std::{env, thread};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":1441,"byte_end":1442,"line_start":45,"line_end":45,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use std::{env, thread};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `thread`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:45:16\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m45\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{env, thread};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `BusinessError` and `BusinessResponse`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":1544,"byte_end":1557,"line_start":48,"line_end":48,"column_start":25,"column_end":38,"is_primary":true,"text":[{"text":"use context::{AppState, BusinessError, BusinessResponse, HttpFileCache};","highlight_start":25,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":1559,"byte_end":1575,"line_start":48,"line_end":48,"column_start":40,"column_end":56,"is_primary":true,"text":[{"text":"use context::{AppState, BusinessError, BusinessResponse, HttpFileCache};","highlight_start":40,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":1542,"byte_end":1575,"line_start":48,"line_end":48,"column_start":23,"column_end":56,"is_primary":true,"text":[{"text":"use context::{AppState, BusinessError, BusinessResponse, HttpFileCache};","highlight_start":23,"highlight_end":56}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `BusinessError` and `BusinessResponse`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:48:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse context::{AppState, BusinessError, BusinessResponse, HttpFileCache};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `HashSet` and `fs`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":33,"byte_end":40,"line_start":1,"line_end":1,"column_start":34,"column_end":41,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":34,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":48,"byte_end":50,"line_start":1,"line_end":1,"column_start":49,"column_end":51,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":49,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":31,"byte_end":40,"line_start":1,"line_end":1,"column_start":32,"column_end":41,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":32,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":23,"byte_end":24,"line_start":1,"line_end":1,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":40,"byte_end":41,"line_start":1,"line_end":1,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":46,"byte_end":50,"line_start":1,"line_end":1,"column_start":47,"column_end":51,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":47,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `HashSet` and `fs`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_web.rs:1:34\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{collections::{HashMap, HashSet}, env, fs};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `put`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":153,"byte_end":156,"line_start":7,"line_end":7,"column_start":16,"column_end":19,"is_primary":true,"text":[{"text":"    get, post, put, web, HttpRequest, HttpResponse, Result","highlight_start":16,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":151,"byte_end":156,"line_start":7,"line_end":7,"column_start":14,"column_end":19,"is_primary":true,"text":[{"text":"    get, post, put, web, HttpRequest, HttpResponse, Result","highlight_start":14,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `put`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_web.rs:7:16\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    get, post, put, web, HttpRequest, HttpResponse, Result\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":589,"byte_end":593,"line_start":15,"line_end":15,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":589,"byte_end":595,"line_start":15,"line_end":15,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":588,"byte_end":589,"line_start":15,"line_end":15,"column_start":17,"column_end":18,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":17,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":600,"byte_end":601,"line_start":15,"line_end":15,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `json`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_web.rs:15:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json::{json, Value};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Cursor`, `cell`, and `thread::current`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":26,"byte_end":30,"line_start":2,"line_end":2,"column_start":11,"column_end":15,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":11,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":74,"byte_end":80,"line_start":2,"line_end":2,"column_start":59,"column_end":65,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":59,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":133,"byte_end":148,"line_start":2,"line_end":2,"column_start":118,"column_end":133,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":118,"highlight_end":133}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":26,"byte_end":32,"line_start":2,"line_end":2,"column_start":11,"column_end":17,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":11,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":74,"byte_end":82,"line_start":2,"line_end":2,"column_start":59,"column_end":67,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":59,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":73,"byte_end":74,"line_start":2,"line_end":2,"column_start":58,"column_end":59,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":58,"highlight_end":59}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":90,"byte_end":91,"line_start":2,"line_end":2,"column_start":75,"column_end":76,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":75,"highlight_end":76}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":131,"byte_end":148,"line_start":2,"line_end":2,"column_start":116,"column_end":133,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":116,"highlight_end":133}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Cursor`, `cell`, and `thread::current`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:2:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `course`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":376,"byte_end":382,"line_start":9,"line_end":9,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"use entity::{course, ","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":376,"byte_end":390,"line_start":9,"line_end":10,"column_start":14,"column_end":5,"is_primary":true,"text":[{"text":"use entity::{course, ","highlight_start":14,"highlight_end":22},{"text":"    section_record::SectionRecordItem, ","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `course`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:9:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse entity::{course, \u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `file`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":945,"byte_end":949,"line_start":21,"line_end":21,"column_start":16,"column_end":20,"is_primary":true,"text":[{"text":"use fs_extra::{file, remove_items};","highlight_start":16,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":945,"byte_end":951,"line_start":21,"line_end":21,"column_start":16,"column_end":22,"is_primary":true,"text":[{"text":"use fs_extra::{file, remove_items};","highlight_start":16,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":944,"byte_end":945,"line_start":21,"line_end":21,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"use fs_extra::{file, remove_items};","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":963,"byte_end":964,"line_start":21,"line_end":21,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use fs_extra::{file, remove_items};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `file`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:21:16\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fs_extra::{file, remove_items};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Reader` and `events::Event`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1085,"byte_end":1098,"line_start":25,"line_end":25,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"use quick_xml::{events::Event, Reader};","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1100,"byte_end":1106,"line_start":25,"line_end":25,"column_start":32,"column_end":38,"is_primary":true,"text":[{"text":"use quick_xml::{events::Event, Reader};","highlight_start":32,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1069,"byte_end":1110,"line_start":25,"line_end":26,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use quick_xml::{events::Event, Reader};","highlight_start":1,"highlight_end":40},{"text":"use sea_orm::{prelude::{Decimal, Json}, sqlx::types::uuid, IsolationLevel, TransactionTrait};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Reader` and `events::Event`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:25:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse quick_xml::{events::Event, Reader};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `sqlx::types::uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1150,"byte_end":1167,"line_start":26,"line_end":26,"column_start":41,"column_end":58,"is_primary":true,"text":[{"text":"use sea_orm::{prelude::{Decimal, Json}, sqlx::types::uuid, IsolationLevel, TransactionTrait};","highlight_start":41,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1148,"byte_end":1167,"line_start":26,"line_end":26,"column_start":39,"column_end":58,"is_primary":true,"text":[{"text":"use sea_orm::{prelude::{Decimal, Json}, sqlx::types::uuid, IsolationLevel, TransactionTrait};","highlight_start":39,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `sqlx::types::uuid`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:26:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sea_orm::{prelude::{Decimal, Json}, sqlx::types::uuid, IsolationLevel, TransactionTrait};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `format`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1364,"byte_end":1370,"line_start":34,"line_end":34,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"use chrono::{format, prelude::*};","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1364,"byte_end":1372,"line_start":34,"line_end":34,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{format, prelude::*};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1363,"byte_end":1364,"line_start":34,"line_end":34,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use chrono::{format, prelude::*};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1382,"byte_end":1383,"line_start":34,"line_end":34,"column_start":32,"column_end":33,"is_primary":true,"text":[{"text":"use chrono::{format, prelude::*};","highlight_start":32,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `format`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:34:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{format, prelude::*};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ListOnlineSessionCount`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1440,"byte_end":1462,"line_start":35,"line_end":35,"column_start":55,"column_end":77,"is_primary":true,"text":[{"text":"use websocket::server::{ClientMessage, ListOnlineIPs, ListOnlineSessionCount};","highlight_start":55,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1438,"byte_end":1462,"line_start":35,"line_end":35,"column_start":53,"column_end":77,"is_primary":true,"text":[{"text":"use websocket::server::{ClientMessage, ListOnlineIPs, ListOnlineSessionCount};","highlight_start":53,"highlight_end":77}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `ListOnlineSessionCount`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:35:55\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse websocket::server::{ClientMessage, ListOnlineIPs, ListOnlineSessionCount};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `create_course_sections`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1510,"byte_end":1532,"line_start":36,"line_end":36,"column_start":45,"column_end":67,"is_primary":true,"text":[{"text":"use crate::{context::ProgressData, course::{create_course_sections, get_course_path, OJContent}};","highlight_start":45,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1510,"byte_end":1534,"line_start":36,"line_end":36,"column_start":45,"column_end":69,"is_primary":true,"text":[{"text":"use crate::{context::ProgressData, course::{create_course_sections, get_course_path, OJContent}};","highlight_start":45,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `create_course_sections`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:36:45\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{context::ProgressData, course::{create_course_sections, get_course_path, OJContent}};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `OIData`, `post_course_download_course_zip`, and `write_log`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1747,"byte_end":1778,"line_start":39,"line_end":39,"column_start":113,"column_end":144,"is_primary":true,"text":[{"text":"use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };","highlight_start":113,"highlight_end":144}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1780,"byte_end":1789,"line_start":39,"line_end":39,"column_start":146,"column_end":155,"is_primary":true,"text":[{"text":"use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };","highlight_start":146,"highlight_end":155}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1801,"byte_end":1807,"line_start":39,"line_end":39,"column_start":167,"column_end":173,"is_primary":true,"text":[{"text":"use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };","highlight_start":167,"highlight_end":173}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1745,"byte_end":1789,"line_start":39,"line_end":39,"column_start":111,"column_end":155,"is_primary":true,"text":[{"text":"use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };","highlight_start":111,"highlight_end":155}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1799,"byte_end":1807,"line_start":39,"line_end":39,"column_start":165,"column_end":173,"is_primary":true,"text":[{"text":"use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };","highlight_start":165,"highlight_end":173}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `OIData`, `post_course_download_course_zip`, and `write_log`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:39:113\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSess\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `form::json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2094,"byte_end":2104,"line_start":48,"line_end":48,"column_start":23,"column_end":33,"is_primary":true,"text":[{"text":"use actix_multipart::{form::json, Multipart};","highlight_start":23,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2094,"byte_end":2106,"line_start":48,"line_end":48,"column_start":23,"column_end":35,"is_primary":true,"text":[{"text":"use actix_multipart::{form::json, Multipart};","highlight_start":23,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":2093,"byte_end":2094,"line_start":48,"line_end":48,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use actix_multipart::{form::json, Multipart};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":2115,"byte_end":2116,"line_start":48,"line_end":48,"column_start":44,"column_end":45,"is_primary":true,"text":[{"text":"use actix_multipart::{form::json, Multipart};","highlight_start":44,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `form::json`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:48:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse actix_multipart::{form::json, Multipart};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `join`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2133,"byte_end":2137,"line_start":49,"line_end":49,"column_start":15,"column_end":19,"is_primary":true,"text":[{"text":"use futures::{join, StreamExt, TryStreamExt};","highlight_start":15,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2133,"byte_end":2139,"line_start":49,"line_end":49,"column_start":15,"column_end":21,"is_primary":true,"text":[{"text":"use futures::{join, StreamExt, TryStreamExt};","highlight_start":15,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `join`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:49:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse futures::{join, StreamExt, TryStreamExt};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rand::Rng`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2202,"byte_end":2211,"line_start":53,"line_end":53,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use rand::Rng; // 用于生成随机数","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2198,"byte_end":2212,"line_start":53,"line_end":53,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"use rand::Rng; // 用于生成随机数","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `rand::Rng`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:53:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand::Rng; // 用于生成随机数\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Sha256`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2250,"byte_end":2256,"line_start":54,"line_end":54,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest}; // 用于生成 sha2","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2250,"byte_end":2258,"line_start":54,"line_end":54,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest}; // 用于生成 sha2","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":2249,"byte_end":2250,"line_start":54,"line_end":54,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest}; // 用于生成 sha2","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":2264,"byte_end":2265,"line_start":54,"line_end":54,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest}; // 用于生成 sha2","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Sha256`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:54:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sha2::{Sha256, Digest}; // 用于生成 sha2\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::fmt::format`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":4,"byte_end":20,"line_start":1,"line_end":1,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use std::fmt::format;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":0,"byte_end":23,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::fmt::format;","highlight_start":1,"highlight_end":22},{"text":"use std::fs::File;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::fmt::format`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::fmt::format;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::fs::File`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":27,"byte_end":40,"line_start":2,"line_end":2,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"use std::fs::File;","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":23,"byte_end":43,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::fs::File;","highlight_start":1,"highlight_end":19},{"text":"use std::io::Write;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::fs::File`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::fs::File;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::os::windows::ffi::OsStrExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":68,"byte_end":99,"line_start":4,"line_end":4,"column_start":5,"column_end":36,"is_primary":true,"text":[{"text":"use std::os::windows::ffi::OsStrExt;","highlight_start":5,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":64,"byte_end":102,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::os::windows::ffi::OsStrExt;","highlight_start":1,"highlight_end":37},{"text":"use std::os::windows::process::CommandExt;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::os::windows::ffi::OsStrExt`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::os::windows::ffi::OsStrExt;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::time::Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":150,"byte_end":169,"line_start":6,"line_end":6,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":146,"byte_end":172,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":25},{"text":"use std::thread;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::time::Duration`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::Duration;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::thread`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":176,"byte_end":187,"line_start":7,"line_end":7,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":172,"byte_end":190,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":1,"highlight_end":17},{"text":"use std::{collections::HashMap, fs, process::Command};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::thread`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::thread;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `mount_virtual_disk` and `unmount_virtual_disk`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":506,"byte_end":524,"line_start":14,"line_end":14,"column_start":127,"column_end":145,"is_primary":true,"text":[{"text":"use service::{copy_recursively, extract_zip, get_client, get_path_in_exe_dir, get_system_version, http_get_file, log_request, mount_virtual_disk, unmount_virtual_disk, write_log, DOWNLOAD_TIME_OUT};","highlight_start":127,"highlight_end":145}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":526,"byte_end":546,"line_start":14,"line_end":14,"column_start":147,"column_end":167,"is_primary":true,"text":[{"text":"use service::{copy_recursively, extract_zip, get_client, get_path_in_exe_dir, get_system_version, http_get_file, log_request, mount_virtual_disk, unmount_virtual_disk, write_log, DOWNLOAD_TIME_OUT};","highlight_start":147,"highlight_end":167}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":504,"byte_end":546,"line_start":14,"line_end":14,"column_start":125,"column_end":167,"is_primary":true,"text":[{"text":"use service::{copy_recursively, extract_zip, get_client, get_path_in_exe_dir, get_system_version, http_get_file, log_request, mount_virtual_disk, unmount_virtual_disk, write_log, DOWNLOAD_TIME_OUT};","highlight_start":125,"highlight_end":167}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `mount_virtual_disk` and `unmount_virtual_disk`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:14:127\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0met_system_version, http_get_file, log_request, mount_virtual_disk, unmount_virtual_disk, write_log, DOWNLOAD_TIME_OUT};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `fs_extra::dir::remove`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":584,"byte_end":605,"line_start":15,"line_end":15,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use fs_extra::dir::remove;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":580,"byte_end":608,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use fs_extra::dir::remove;","highlight_start":1,"highlight_end":27},{"text":"use serde_json::Value;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `fs_extra::dir::remove`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fs_extra::dir::remove;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `FindWindowW`, `HWND_TOPMOST`, `SWP_NOMOVE`, `SWP_NOSIZE`, and `SetWindowPos`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":897,"byte_end":908,"line_start":24,"line_end":24,"column_start":27,"column_end":38,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":27,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":910,"byte_end":922,"line_start":24,"line_end":24,"column_start":40,"column_end":52,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":40,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":924,"byte_end":936,"line_start":24,"line_end":24,"column_start":54,"column_end":66,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":54,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":938,"byte_end":948,"line_start":24,"line_end":24,"column_start":68,"column_end":78,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":68,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":950,"byte_end":960,"line_start":24,"line_end":24,"column_start":80,"column_end":90,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":80,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":871,"byte_end":964,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":1,"highlight_end":92},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `FindWindowW`, `HWND_TOPMOST`, `SWP_NOMOVE`, `SWP_NOSIZE`, and `SetWindowPos`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:24:27\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `task` and `time`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":1061,"byte_end":1065,"line_start":30,"line_end":30,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"use tokio::{io, task, time};","highlight_start":17,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":1067,"byte_end":1071,"line_start":30,"line_end":30,"column_start":23,"column_end":27,"is_primary":true,"text":[{"text":"use tokio::{io, task, time};","highlight_start":23,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":1059,"byte_end":1071,"line_start":30,"line_end":30,"column_start":15,"column_end":27,"is_primary":true,"text":[{"text":"use tokio::{io, task, time};","highlight_start":15,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":1056,"byte_end":1057,"line_start":30,"line_end":30,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use tokio::{io, task, time};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":1071,"byte_end":1072,"line_start":30,"line_end":30,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"use tokio::{io, task, time};","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `task` and `time`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:30:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::{io, task, time};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Cursor`, `HashSet`, `Instant`, and `Stdio`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":49,"byte_end":56,"line_start":2,"line_end":2,"column_start":34,"column_end":41,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":34,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":69,"byte_end":75,"line_start":2,"line_end":2,"column_start":54,"column_end":60,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":54,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":120,"byte_end":125,"line_start":2,"line_end":2,"column_start":105,"column_end":110,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":105,"highlight_end":110}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":145,"byte_end":152,"line_start":2,"line_end":2,"column_start":130,"column_end":137,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":130,"highlight_end":137}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":47,"byte_end":56,"line_start":2,"line_end":2,"column_start":32,"column_end":41,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":32,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":39,"byte_end":40,"line_start":2,"line_end":2,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":56,"byte_end":57,"line_start":2,"line_end":2,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":69,"byte_end":77,"line_start":2,"line_end":2,"column_start":54,"column_end":62,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":54,"highlight_end":62}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":67,"byte_end":69,"line_start":2,"line_end":2,"column_start":52,"column_end":54,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":52,"highlight_end":54}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":82,"byte_end":83,"line_start":2,"line_end":2,"column_start":67,"column_end":68,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":67,"highlight_end":68}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":117,"byte_end":125,"line_start":2,"line_end":2,"column_start":102,"column_end":110,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":102,"highlight_end":110}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":109,"byte_end":110,"line_start":2,"line_end":2,"column_start":94,"column_end":95,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":94,"highlight_end":95}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":125,"byte_end":126,"line_start":2,"line_end":2,"column_start":110,"column_end":111,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":110,"highlight_end":111}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":143,"byte_end":152,"line_start":2,"line_end":2,"column_start":128,"column_end":137,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":128,"highlight_end":137}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":134,"byte_end":135,"line_start":2,"line_end":2,"column_start":119,"column_end":120,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":119,"highlight_end":120}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":152,"byte_end":153,"line_start":2,"line_end":2,"column_start":137,"column_end":138,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":137,"highlight_end":138}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Cursor`, `HashSet`, `Instant`, and `Stdio`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2:34\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `HttpResponseBuilder`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":340,"byte_end":359,"line_start":8,"line_end":8,"column_start":65,"column_end":84,"is_primary":true,"text":[{"text":"use actix_web::{get, post, put, web, HttpRequest, HttpResponse, HttpResponseBuilder, Result};","highlight_start":65,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":338,"byte_end":359,"line_start":8,"line_end":8,"column_start":63,"column_end":84,"is_primary":true,"text":[{"text":"use actix_web::{get, post, put, web, HttpRequest, HttpResponse, HttpResponseBuilder, Result};","highlight_start":63,"highlight_end":84}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `HttpResponseBuilder`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:8:65\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse actix_web::{get, post, put, web, HttpRequest, HttpResponse, HttpResponseBuilder, Result};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Context`, `JsValue`, `json`, `object::JsObject`, `object::Object`, and `property::Attribute`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":392,"byte_end":396,"line_start":9,"line_end":9,"column_start":22,"column_end":26,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":22,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":398,"byte_end":412,"line_start":9,"line_end":9,"column_start":28,"column_end":42,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":28,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":415,"byte_end":431,"line_start":9,"line_end":9,"column_start":45,"column_end":61,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":45,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":433,"byte_end":452,"line_start":9,"line_end":9,"column_start":63,"column_end":82,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":63,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":454,"byte_end":461,"line_start":9,"line_end":9,"column_start":84,"column_end":91,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":84,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":463,"byte_end":470,"line_start":9,"line_end":9,"column_start":93,"column_end":100,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":93,"highlight_end":100}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":371,"byte_end":474,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":1,"highlight_end":102},{"text":"// use rquickjs::{Context, Runtime, Value};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Context`, `JsValue`, `json`, `object::JsObject`, `object::Object`, and `property::Attribute`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:9:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":543,"byte_end":547,"line_start":12,"line_end":12,"column_start":23,"column_end":27,"is_primary":true,"text":[{"text":"use entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::SectionRecordItem};","highlight_start":23,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":543,"byte_end":550,"line_start":12,"line_end":12,"column_start":23,"column_end":30,"is_primary":true,"text":[{"text":"use entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::SectionRecordItem};","highlight_start":23,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":542,"byte_end":543,"line_start":12,"line_end":12,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::SectionRecordItem};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":562,"byte_end":563,"line_start":12,"line_end":12,"column_start":42,"column_end":43,"is_primary":true,"text":[{"text":"use entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::SectionRecordItem};","highlight_start":42,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `self`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:12:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::SectionRecordItem};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `log::kv::source`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":703,"byte_end":718,"line_start":14,"line_end":14,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"use log::kv::source;","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":699,"byte_end":721,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use log::kv::source;","highlight_start":1,"highlight_end":21},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `log::kv::source`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::kv::source;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `controller_admin`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1015,"byte_end":1031,"line_start":27,"line_end":27,"column_start":13,"column_end":29,"is_primary":true,"text":[{"text":"use crate::{controller_admin, faye};","highlight_start":13,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1015,"byte_end":1033,"line_start":27,"line_end":27,"column_start":13,"column_end":31,"is_primary":true,"text":[{"text":"use crate::{controller_admin, faye};","highlight_start":13,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":1014,"byte_end":1015,"line_start":27,"line_end":27,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use crate::{controller_admin, faye};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":1037,"byte_end":1038,"line_start":27,"line_end":27,"column_start":35,"column_end":36,"is_primary":true,"text":[{"text":"use crate::{controller_admin, faye};","highlight_start":35,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `controller_admin`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:27:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{controller_admin, faye};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `HxrError` and `NORMAL_TIME_OUT`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1275,"byte_end":1283,"line_start":31,"line_end":31,"column_start":134,"column_end":142,"is_primary":true,"text":[{"text":"use service::{extract_zip, get_path_in_exe_dir, get_system_version, log_request, post_micro_app_file, post_scratch_file, CodeResult, HxrError, MicroAppCodeResult, Mutation, Query, NORMAL_TIME_OUT};","highlight_start":134,"highlight_end":142}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":1322,"byte_end":1337,"line_start":31,"line_end":31,"column_start":181,"column_end":196,"is_primary":true,"text":[{"text":"use service::{extract_zip, get_path_in_exe_dir, get_system_version, log_request, post_micro_app_file, post_scratch_file, CodeResult, HxrError, MicroAppCodeResult, Mutation, Query, NORMAL_TIME_OUT};","highlight_start":181,"highlight_end":196}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1273,"byte_end":1283,"line_start":31,"line_end":31,"column_start":132,"column_end":142,"is_primary":true,"text":[{"text":"use service::{extract_zip, get_path_in_exe_dir, get_system_version, log_request, post_micro_app_file, post_scratch_file, CodeResult, HxrError, MicroAppCodeResult, Mutation, Query, NORMAL_TIME_OUT};","highlight_start":132,"highlight_end":142}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":1320,"byte_end":1337,"line_start":31,"line_end":31,"column_start":179,"column_end":196,"is_primary":true,"text":[{"text":"use service::{extract_zip, get_path_in_exe_dir, get_system_version, log_request, post_micro_app_file, post_scratch_file, CodeResult, HxrError, MicroAppCodeResult, Mutation, Query, NORMAL_TIME_OUT};","highlight_start":179,"highlight_end":196}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `HxrError` and `NORMAL_TIME_OUT`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:31:134\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mile, post_scratch_file, CodeResult, HxrError, MicroAppCodeResult, Mutation, Query, NORMAL_TIME_OUT};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `BufReader`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1415,"byte_end":1424,"line_start":38,"line_end":38,"column_start":15,"column_end":24,"is_primary":true,"text":[{"text":"use std::io::{BufReader, Read};","highlight_start":15,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1415,"byte_end":1426,"line_start":38,"line_end":38,"column_start":15,"column_end":26,"is_primary":true,"text":[{"text":"use std::io::{BufReader, Read};","highlight_start":15,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":1414,"byte_end":1415,"line_start":38,"line_end":38,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use std::io::{BufReader, Read};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":1430,"byte_end":1431,"line_start":38,"line_end":38,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use std::io::{BufReader, Read};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `BufReader`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:38:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::{BufReader, Read};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `actix_http::StatusCode`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1459,"byte_end":1481,"line_start":40,"line_end":40,"column_start":5,"column_end":27,"is_primary":true,"text":[{"text":"use actix_http::StatusCode;","highlight_start":5,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1455,"byte_end":1484,"line_start":40,"line_end":41,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use actix_http::StatusCode;","highlight_start":1,"highlight_end":28},{"text":"use futures_util::{SinkExt, StreamExt};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `actix_http::StatusCode`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:40:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse actix_http::StatusCode;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `quick_xml::events::Event`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1529,"byte_end":1553,"line_start":42,"line_end":42,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"use quick_xml::events::Event;","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1525,"byte_end":1556,"line_start":42,"line_end":43,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use quick_xml::events::Event;","highlight_start":1,"highlight_end":30},{"text":"use quick_xml::Reader;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `quick_xml::events::Event`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:42:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse quick_xml::events::Event;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `quick_xml::Reader`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1560,"byte_end":1577,"line_start":43,"line_end":43,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"use quick_xml::Reader;","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1556,"byte_end":1580,"line_start":43,"line_end":44,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use quick_xml::Reader;","highlight_start":1,"highlight_end":23},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `quick_xml::Reader`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:43:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse quick_xml::Reader;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::faye::FayeMessageRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1636,"byte_end":1667,"line_start":47,"line_end":47,"column_start":5,"column_end":36,"is_primary":true,"text":[{"text":"use crate::faye::FayeMessageRequest;","highlight_start":5,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1632,"byte_end":1670,"line_start":47,"line_end":48,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::faye::FayeMessageRequest;","highlight_start":1,"highlight_end":37},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::faye::FayeMessageRequest`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:47:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::faye::FayeMessageRequest;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `terminal`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":7659,"byte_end":7667,"line_start":199,"line_end":199,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let terminal = match Query::find_terminal_by_user_id(&txn, user_record.id).await {","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":7659,"byte_end":7667,"line_start":199,"line_end":199,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let terminal = match Query::find_terminal_by_user_id(&txn, user_record.id).await {","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_terminal","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `terminal`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_web.rs:199:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let terminal = match Query::find_terminal_by_user_id(&txn, user_record.id).await {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_terminal`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `terminal_user_registry_records`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":127729,"byte_end":127759,"line_start":3032,"line_end":3032,"column_start":9,"column_end":39,"is_primary":true,"text":[{"text":"    let terminal_user_registry_records = match Query::find_terminal_by_ips(&txn, online_ips.clone()).await {","highlight_start":9,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":127729,"byte_end":127759,"line_start":3032,"line_end":3032,"column_start":9,"column_end":39,"is_primary":true,"text":[{"text":"    let terminal_user_registry_records = match Query::find_terminal_by_ips(&txn, online_ips.clone()).await {","highlight_start":9,"highlight_end":39}],"label":null,"suggested_replacement":"_terminal_user_registry_records","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `terminal_user_registry_records`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:3032:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3032\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let terminal_user_registry_records = match Query::find_terminal_by_ips(&txn, online_ips.clone()).await {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_terminal_user_registry_records`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `terminal_user_not_online_registry_records`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":128077,"byte_end":128118,"line_start":3040,"line_end":3040,"column_start":9,"column_end":50,"is_primary":true,"text":[{"text":"    let terminal_user_not_online_registry_records = match Query::find_terminal_not_online_by_ips(&txn, online_ips.clone()).await {","highlight_start":9,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":128077,"byte_end":128118,"line_start":3040,"line_end":3040,"column_start":9,"column_end":50,"is_primary":true,"text":[{"text":"    let terminal_user_not_online_registry_records = match Query::find_terminal_not_online_by_ips(&txn, online_ips.clone()).await {","highlight_start":9,"highlight_end":50}],"label":null,"suggested_replacement":"_terminal_user_not_online_registry_records","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `terminal_user_not_online_registry_records`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:3040:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3040\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let terminal_user_not_online_registry_records = match Query::find_terminal_not_online_by_ips(&txn, online_ips.clone()).await {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_terminal_user_not_online_registry_records`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `train_plan_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":149191,"byte_end":149204,"line_start":3527,"line_end":3527,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let train_plan_id:i32 = match current_train_plan_id.parse::<i32>() {","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":149191,"byte_end":149204,"line_start":3527,"line_end":3527,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let train_plan_id:i32 = match current_train_plan_id.parse::<i32>() {","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_train_plan_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `train_plan_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:3527:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3527\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let train_plan_id:i32 = match current_train_plan_id.parse::<i32>() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_train_plan_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `train_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":150658,"byte_end":150666,"line_start":3568,"line_end":3568,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let train_id = match old_terminal.train_id {","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":150658,"byte_end":150666,"line_start":3568,"line_end":3568,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let train_id = match old_terminal.train_id {","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_train_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `train_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:3568:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3568\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let train_id = match old_terminal.train_id {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_train_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `key` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":200307,"byte_end":200310,"line_start":4839,"line_end":4839,"column_start":13,"column_end":16,"is_primary":true,"text":[{"text":"    let mut key = String::new();","highlight_start":13,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `key` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:4839:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4839\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut key = String::new();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `course_type` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":201566,"byte_end":201577,"line_start":4865,"line_end":4865,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"    let mut course_type = String::new();","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_course_type` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `course_type` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:4865:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4865\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_type = String::new();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_course_type` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_type` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":202379,"byte_end":202390,"line_start":4878,"line_end":4878,"column_start":21,"column_end":32,"is_primary":true,"text":[{"text":"                    course_type = section_value[\"sectionType\"].as_str().unwrap().to_string();","highlight_start":21,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_type` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:4878:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4878\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    course_type = section_value[\"sectionType\"].as_str().unwrap().to_string();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `os_version`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":20218,"byte_end":20228,"line_start":511,"line_end":511,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":20218,"byte_end":20228,"line_start":511,"line_end":511,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"_os_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `os_version`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:511:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m511\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let os_version = match get_system_version(){\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_os_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":26858,"byte_end":26865,"line_start":663,"line_end":663,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    user_id: web::Path<String>,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":26858,"byte_end":26865,"line_start":663,"line_end":663,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    user_id: web::Path<String>,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `user_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:663:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m663\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    user_id: web::Path<String>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `course_type` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":2332,"byte_end":2343,"line_start":64,"line_end":64,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"    let mut course_type = String::new();","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_course_type` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `course_type` is assigned to, but never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:64:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m64\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_type = String::new();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_course_type` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_type` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":3121,"byte_end":3132,"line_start":77,"line_end":77,"column_start":21,"column_end":32,"is_primary":true,"text":[{"text":"                    course_type = section_value[\"sectionType\"].as_str().unwrap().to_string();","highlight_start":21,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_type` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:77:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    course_type = section_value[\"sectionType\"].as_str().unwrap().to_string();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `value`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":5806,"byte_end":5811,"line_start":135,"line_end":135,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    let value = reqwest::Client::new()","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":5806,"byte_end":5811,"line_start":135,"line_end":135,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    let value = reqwest::Client::new()","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":"_value","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `value`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:135:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let value = reqwest::Client::new()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_value`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"api\\src\\course.rs","byte_start":27124,"byte_end":27128,"line_start":598,"line_end":598,"column_start":62,"column_end":66,"is_primary":true,"text":[{"text":"                    start_download_service(course_slug, url, tx11).await;","highlight_start":62,"highlight_end":66}],"label":"expected `Option<UnboundedSender<...>>`, found `UnboundedSender<FayeMessageRequest>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":27083,"byte_end":27105,"line_start":598,"line_end":598,"column_start":21,"column_end":43,"is_primary":false,"text":[{"text":"                    start_download_service(course_slug, url, tx11).await;","highlight_start":21,"highlight_end":43}],"label":"arguments to this function are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected enum `std::option::Option<tokio::sync::mpsc::UnboundedSender<_>>`\n found struct `tokio::sync::mpsc::UnboundedSender<_>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"function defined here","code":null,"level":"note","spans":[{"file_name":"api\\src\\course.rs","byte_start":28072,"byte_end":28143,"line_start":625,"line_end":625,"column_start":5,"column_end":76,"is_primary":false,"text":[{"text":"    tx_faye: Option<tokio::sync::mpsc::UnboundedSender<FayeMessageRequest>>","highlight_start":5,"highlight_end":76}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":27994,"byte_end":28016,"line_start":622,"line_end":622,"column_start":10,"column_end":32,"is_primary":true,"text":[{"text":"async fn start_download_service(","highlight_start":10,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"try wrapping the expression in `Some`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":27124,"byte_end":27124,"line_start":598,"line_end":598,"column_start":62,"column_end":62,"is_primary":true,"text":[{"text":"                    start_download_service(course_slug, url, tx11).await;","highlight_start":62,"highlight_end":62}],"label":null,"suggested_replacement":"Some(","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":27128,"byte_end":27128,"line_start":598,"line_end":598,"column_start":66,"column_end":66,"is_primary":true,"text":[{"text":"                    start_download_service(course_slug, url, tx11).await;","highlight_start":66,"highlight_end":66}],"label":null,"suggested_replacement":")","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:598:62\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m598\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    start_download_service(course_slug, url, tx11).await;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Option<UnboundedSender<...>>`, found `UnboundedSender<FayeMessageRequest>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14marguments to this function are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected enum `\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::option::Option<\u001b[0m\u001b[0mtokio::sync::mpsc::UnboundedSender<_>\u001b[0m\u001b[0m\u001b[1m\u001b[35m>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m             found struct `tokio::sync::mpsc::UnboundedSender<_>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:622:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m622\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn start_download_service(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m625\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    tx_faye: Option<tokio::sync::mpsc::UnboundedSender<FayeMessageRequest>>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------------------------------------------------------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: try wrapping the expression in `Some`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m598\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                    start_download_service(course_slug, url, \u001b[0m\u001b[0m\u001b[38;5;10mSome(\u001b[0m\u001b[0mtx11\u001b[0m\u001b[0m\u001b[38;5;10m)\u001b[0m\u001b[0m).await;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                              \u001b[0m\u001b[0m\u001b[38;5;10m+++++\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10m+\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"api\\src\\course.rs","byte_start":29740,"byte_end":29760,"line_start":666,"line_end":666,"column_start":87,"column_end":107,"is_primary":true,"text":[{"text":"            if let Err(e) = writer.send(tokio_tungstenite::tungstenite::Message::Text(progress.to_string())).await {","highlight_start":87,"highlight_end":107}],"label":"expected `Utf8Bytes`, found `String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":29694,"byte_end":29739,"line_start":666,"line_end":666,"column_start":41,"column_end":86,"is_primary":false,"text":[{"text":"            if let Err(e) = writer.send(tokio_tungstenite::tungstenite::Message::Text(progress.to_string())).await {","highlight_start":41,"highlight_end":86}],"label":"arguments to this enum variant are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"tuple variant defined here","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tungstenite-0.28.0\\src\\protocol\\message.rs","byte_start":5165,"byte_end":5169,"line_start":159,"line_end":159,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    Text(Utf8Bytes),","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"call `Into::into` on this expression to convert `std::string::String` into `Utf8Bytes`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":29760,"byte_end":29760,"line_start":666,"line_end":666,"column_start":107,"column_end":107,"is_primary":true,"text":[{"text":"            if let Err(e) = writer.send(tokio_tungstenite::tungstenite::Message::Text(progress.to_string())).await {","highlight_start":107,"highlight_end":107}],"label":null,"suggested_replacement":".into()","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:666:87\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m666\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Err(e) = writer.send(tokio_tungstenite::tungstenite::Message::Text(progress.to_string())).await {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `Utf8Bytes`, found `String`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14marguments to this enum variant are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: tuple variant defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tungstenite-0.28.0\\src\\protocol\\message.rs:159:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Text(Utf8Bytes),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: call `Into::into` on this expression to convert `std::string::String` into `Utf8Bytes`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m666\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            if let Err(e) = writer.send(tokio_tungstenite::tungstenite::Message::Text(progress.to_string()\u001b[0m\u001b[0m\u001b[38;5;10m.into()\u001b[0m\u001b[0m)).await {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                           \u001b[0m\u001b[0m\u001b[38;5;10m+++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `course_type` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":35118,"byte_end":35129,"line_start":853,"line_end":853,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"    let mut course_type:String = \"\".to_string();","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_course_type` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `course_type` is assigned to, but never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:853:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m853\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_type:String = \"\".to_string();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_course_type` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_type` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":35850,"byte_end":35861,"line_start":867,"line_end":867,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        course_type = params[\"course_type\"].to_string();","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_type` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:867:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m867\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        course_type = params[\"course_type\"].to_string();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `train_course` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":37910,"byte_end":37922,"line_start":910,"line_end":910,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"    let mut train_course: Value = serde_json::Value::Array(Vec::new());","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `train_course` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:910:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m910\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut train_course: Value = serde_json::Value::Array(Vec::new());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_res` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":84568,"byte_end":84578,"line_start":1855,"line_end":1855,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"    let mut record_res: HashMap<String, Value> = HashMap::new();","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_res` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1855:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1855\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut record_res: HashMap<String, Value> = HashMap::new();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `raw_answer` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":87740,"byte_end":87750,"line_start":1915,"line_end":1915,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"                    raw_answer = raw_answer.replace(\"\\\"\", \"\");","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `raw_answer` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1915:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1915\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    raw_answer = raw_answer.replace(\"\\\"\", \"\");\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":105674,"byte_end":105683,"line_start":2339,"line_end":2339,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    app_state: web::Data<AppState>","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":105674,"byte_end":105683,"line_start":2339,"line_end":2339,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    app_state: web::Data<AppState>","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":"_app_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_state`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2339:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2339\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_state: web::Data<AppState>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `cell_type` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":114760,"byte_end":114769,"line_start":2558,"line_end":2558,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut cell_type: Option<String> = None;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_cell_type` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `cell_type` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2558:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2558\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut cell_type: Option<String> = None;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_cell_type` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `cell_type` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":115148,"byte_end":115157,"line_start":2566,"line_end":2566,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"            cell_type = match item[\"metadata\"][\"type\" ].as_str() {","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `cell_type` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2566:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2566\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            cell_type = match item[\"metadata\"][\"type\" ].as_str() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `inner_data`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":117831,"byte_end":117841,"line_start":2627,"line_end":2627,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"        if let Some(inner_data) = json_value.get(uuid.clone()) {","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":117831,"byte_end":117841,"line_start":2627,"line_end":2627,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"        if let Some(inner_data) = json_value.get(uuid.clone()) {","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":"_inner_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `inner_data`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2627:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2627\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(inner_data) = json_value.get(uuid.clone()) {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_inner_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `oj_result` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":130174,"byte_end":130183,"line_start":2929,"line_end":2929,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut oj_result: Value = Value::Null;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `oj_result` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2929:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2929\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut oj_result: Value = Value::Null;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `os_version`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":130674,"byte_end":130684,"line_start":2940,"line_end":2940,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":130674,"byte_end":130684,"line_start":2940,"line_end":2940,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"_os_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `os_version`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2940:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2940\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let os_version = match get_system_version(){\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_os_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `if_load_code`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":159394,"byte_end":159406,"line_start":3579,"line_end":3579,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let if_load_code = params.if_load_code.clone();","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":159394,"byte_end":159406,"line_start":3579,"line_end":3579,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let if_load_code = params.if_load_code.clone();","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_if_load_code","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `if_load_code`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3579:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3579\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let if_load_code = params.if_load_code.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_if_load_code`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_content` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":162128,"byte_end":162142,"line_start":3640,"line_end":3640,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut course_content: Value = serde_json::Value::Array(Vec::new());","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_content` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3640:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3640\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_content: Value = serde_json::Value::Array(Vec::new());\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `course_id` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":170426,"byte_end":170435,"line_start":3801,"line_end":3801,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_course_id` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `course_id` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3801:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3801\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_id ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_course_id` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `save_code` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":170451,"byte_end":170460,"line_start":3802,"line_end":3802,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_save_code` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `save_code` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3802:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3802\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_code ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_save_code` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `save_run_result` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":170476,"byte_end":170491,"line_start":3803,"line_end":3803,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_save_run_result` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `save_run_result` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3803:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3803\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_run_result ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_save_run_result` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `container_info` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":170507,"byte_end":170521,"line_start":3804,"line_end":3804,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut container_info = None;","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `container_info` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3804:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3804\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut container_info = None;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_id` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":170597,"byte_end":170606,"line_start":3807,"line_end":3807,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        course_id = course_info_item.id;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_id` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3807:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3807\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        course_id = course_info_item.id;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `save_code` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":170639,"byte_end":170648,"line_start":3808,"line_end":3808,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        save_code = course_info_item.save_code;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `save_code` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3808:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3808\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        save_code = course_info_item.save_code;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `save_run_result` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":170688,"byte_end":170703,"line_start":3809,"line_end":3809,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        save_run_result = course_info_item.save_run_result;","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `save_run_result` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3809:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3809\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        save_run_result = course_info_item.save_run_result;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":170422,"byte_end":170435,"line_start":3801,"line_end":3801,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":170422,"byte_end":170426,"line_start":3801,"line_end":3801,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3801:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3801\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_id ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":170447,"byte_end":170460,"line_start":3802,"line_end":3802,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":170447,"byte_end":170451,"line_start":3802,"line_end":3802,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3802:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3802\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_code ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":170472,"byte_end":170491,"line_start":3803,"line_end":3803,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":170472,"byte_end":170476,"line_start":3803,"line_end":3803,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3803:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3803\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_run_result ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `section_id` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":206884,"byte_end":206894,"line_start":4515,"line_end":4515,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"    let mut section_id = 0;","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_section_id` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `section_id` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4515:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4515\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut section_id = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_section_id` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `status` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":207130,"byte_end":207136,"line_start":4520,"line_end":4520,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"    let mut status = None;","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_status` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `status` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4520:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4520\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut status = None;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_status` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `section_id` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":207788,"byte_end":207798,"line_start":4531,"line_end":4531,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"                    section_id = section_value[\"sectionID\"].as_i64().unwrap() as i32;","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `section_id` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4531:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4531\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    section_id = section_value[\"sectionID\"].as_i64().unwrap() as i32;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":208050,"byte_end":208056,"line_start":4534,"line_end":4534,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"                    status = section_value[\"status\"].as_bool();","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4534:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4534\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    status = section_value[\"status\"].as_bool();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":226659,"byte_end":226668,"line_start":4997,"line_end":4997,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    app_state: web::Data<AppState>,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":226659,"byte_end":226668,"line_start":4997,"line_end":4997,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    app_state: web::Data<AppState>,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":"_app_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_state`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4997:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4997\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_state: web::Data<AppState>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `course_slug`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":226696,"byte_end":226707,"line_start":4998,"line_end":4998,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    course_slug: web::Path<String>","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":226696,"byte_end":226707,"line_start":4998,"line_end":4998,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    course_slug: web::Path<String>","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":"_course_slug","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `course_slug`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4998:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4998\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    course_slug: web::Path<String>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_course_slug`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":227157,"byte_end":227164,"line_start":5009,"line_end":5009,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"    let user_id = user_id.unwrap();","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":227157,"byte_end":227164,"line_start":5009,"line_end":5009,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"    let user_id = user_id.unwrap();","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `user_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5009:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5009\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let user_id = user_id.unwrap();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `path_dir`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":227194,"byte_end":227202,"line_start":5010,"line_end":5010,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let path_dir = get_path_in_exe_dir(\"course\");","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":227194,"byte_end":227202,"line_start":5010,"line_end":5010,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let path_dir = get_path_in_exe_dir(\"course\");","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_path_dir","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `path_dir`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5010:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5010\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let path_dir = get_path_in_exe_dir(\"course\");\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_path_dir`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":231791,"byte_end":231805,"line_start":5127,"line_end":5127,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":231791,"byte_end":231795,"line_start":5127,"line_end":5127,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5127:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut udp_server = app_state.udp_server.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `udp_server`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":235987,"byte_end":235997,"line_start":5214,"line_end":5214,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":235987,"byte_end":235997,"line_start":5214,"line_end":5214,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_udp_server","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `udp_server`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5214:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut udp_server = app_state.udp_server.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_udp_server`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":235983,"byte_end":235997,"line_start":5214,"line_end":5214,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":235983,"byte_end":235987,"line_start":5214,"line_end":5214,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5214:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut udp_server = app_state.udp_server.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable expression","code":{"code":"unreachable_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":246706,"byte_end":246720,"line_start":5479,"line_end":5479,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"            \"\".to_string()","highlight_start":13,"highlight_end":27}],"label":"unreachable expression","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":246614,"byte_end":246691,"line_start":5478,"line_end":5478,"column_start":13,"column_end":80,"is_primary":false,"text":[{"text":"            return Err(BusinessError::ProcessError { reason: format!(\"未开始上课\")});","highlight_start":13,"highlight_end":80}],"label":"any code following this expression is unreachable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unreachable_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable expression\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5479:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5478\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            return Err(BusinessError::ProcessError { reason: format!(\"未开始上课\")});\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14many code following this expression is unreachable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5479\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"\".to_string()\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munreachable expression\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unreachable_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `os_version`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":249086,"byte_end":249096,"line_start":5540,"line_end":5540,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":249086,"byte_end":249096,"line_start":5540,"line_end":5540,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"_os_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `os_version`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5540:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5540\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let os_version = match get_system_version(){\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_os_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":249401,"byte_end":249416,"line_start":5545,"line_end":5545,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    let mut python_path = get_path_in_exe_dir(\"../ipython\").join(\"python-3.8.20-embed-amd64\");","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":249401,"byte_end":249405,"line_start":5545,"line_end":5545,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut python_path = get_path_in_exe_dir(\"../ipython\").join(\"python-3.8.20-embed-amd64\");","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5545:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5545\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut python_path = get_path_in_exe_dir(\"../ipython\").join(\"python-3.8.20-embed-amd64\");\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `is_admin` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":254942,"byte_end":254950,"line_start":5689,"line_end":5689,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"    let mut is_admin = true;","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_is_admin` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `is_admin` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5689:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5689\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut is_admin = true;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_is_admin` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `if_load_code`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":255205,"byte_end":255217,"line_start":5697,"line_end":5697,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let if_load_code = true;","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":255205,"byte_end":255217,"line_start":5697,"line_end":5697,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let if_load_code = true;","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_if_load_code","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `if_load_code`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5697:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5697\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let if_load_code = true;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_if_load_code`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `submit_record`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":255479,"byte_end":255492,"line_start":5705,"line_end":5705,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let submit_record = json!({});","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":255479,"byte_end":255492,"line_start":5705,"line_end":5705,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let submit_record = json!({});","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_submit_record","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `submit_record`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5705:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5705\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let submit_record = json!({});\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_submit_record`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `status` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":255948,"byte_end":255954,"line_start":5716,"line_end":5716,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"    let mut status = None;","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_status` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `status` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5716:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5716\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut status = None;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_status` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":256868,"byte_end":256874,"line_start":5730,"line_end":5730,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"                    status = section_value[\"status\"].as_bool();","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5730:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5730\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    status = section_value[\"status\"].as_bool();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `stat`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":257514,"byte_end":257518,"line_start":5744,"line_end":5744,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let stat = json!({","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":257514,"byte_end":257518,"line_start":5744,"line_end":5744,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let stat = json!({","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_stat","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `stat`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5744:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5744\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let stat = json!({\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_stat`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_content` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":257917,"byte_end":257931,"line_start":5758,"line_end":5758,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut course_content: Value = serde_json::Value::Array(Vec::new());","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_content` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5758:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5758\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_content: Value = serde_json::Value::Array(Vec::new());\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `is_admin` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":262722,"byte_end":262730,"line_start":5854,"line_end":5854,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        is_admin = false;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `is_admin` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5854:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5854\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        is_admin = false;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `course_id` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263106,"byte_end":263115,"line_start":5864,"line_end":5864,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_course_id` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `course_id` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5864:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5864\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_id ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_course_id` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `save_code` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263131,"byte_end":263140,"line_start":5865,"line_end":5865,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_save_code` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `save_code` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5865:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5865\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_code ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_save_code` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `save_run_result` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263156,"byte_end":263171,"line_start":5866,"line_end":5866,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_save_run_result` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `save_run_result` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5866:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5866\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_run_result ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_save_run_result` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `container_info` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263187,"byte_end":263201,"line_start":5867,"line_end":5867,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut container_info = None;","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_container_info` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `container_info` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5867:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5867\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut container_info = None;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_container_info` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_id` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263277,"byte_end":263286,"line_start":5870,"line_end":5870,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        course_id = course_info_item.id;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_id` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5870:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5870\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        course_id = course_info_item.id;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `save_code` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263319,"byte_end":263328,"line_start":5871,"line_end":5871,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        save_code = course_info_item.save_code;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `save_code` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5871:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5871\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        save_code = course_info_item.save_code;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `save_run_result` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263368,"byte_end":263383,"line_start":5872,"line_end":5872,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        save_run_result = course_info_item.save_run_result;","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `save_run_result` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5872:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5872\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        save_run_result = course_info_item.save_run_result;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `container_info` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263429,"byte_end":263443,"line_start":5873,"line_end":5873,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"        container_info = course_info_item.container_info;","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `container_info` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5873:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5873\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        container_info = course_info_item.container_info;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `teams`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263662,"byte_end":263667,"line_start":5880,"line_end":5880,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    let teams = match Query::all_team_find(&txn).await  {","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":263662,"byte_end":263667,"line_start":5880,"line_end":5880,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    let teams = match Query::all_team_find(&txn).await  {","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":"_teams","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `teams`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5880:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5880\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let teams = match Query::all_team_find(&txn).await  {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_teams`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `record_time` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":264438,"byte_end":264449,"line_start":5900,"line_end":5900,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"    let mut record_time = Value::Null;","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_record_time` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `record_time` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5900:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5900\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut record_time = Value::Null;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_record_time` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_time` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":264764,"byte_end":264775,"line_start":5909,"line_end":5909,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        record_time = record_times_json;","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_time` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5909:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5909\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        record_time = record_times_json;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `record_message` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":264983,"byte_end":264997,"line_start":5917,"line_end":5917,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut record_message = json!({});","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_record_message` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `record_message` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5917:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5917\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut record_message = json!({});\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_record_message` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_message` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":271024,"byte_end":271038,"line_start":6025,"line_end":6025,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"                record_message = serde_json::to_value(record_res).unwrap();","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_message` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:6025:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6025\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                record_message = serde_json::to_value(record_res).unwrap();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_message` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":271466,"byte_end":271480,"line_start":6033,"line_end":6033,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"                record_message = serde_json::to_value(record_map).unwrap();","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_message` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:6033:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6033\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                record_message = serde_json::to_value(record_map).unwrap();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_message` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":271664,"byte_end":271678,"line_start":6038,"line_end":6038,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"                record_message = record;","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_message` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:6038:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6038\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                record_message = record;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_message` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":273202,"byte_end":273216,"line_start":6066,"line_end":6066,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"                record_message = code_result;","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_message` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:6066:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6066\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                record_message = code_result;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263102,"byte_end":263115,"line_start":5864,"line_end":5864,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":263102,"byte_end":263106,"line_start":5864,"line_end":5864,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5864:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5864\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_id ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263127,"byte_end":263140,"line_start":5865,"line_end":5865,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":263127,"byte_end":263131,"line_start":5865,"line_end":5865,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5865:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5865\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_code ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":263152,"byte_end":263171,"line_start":5866,"line_end":5866,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":263152,"byte_end":263156,"line_start":5866,"line_end":5866,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5866:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5866\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_run_result ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":266610,"byte_end":266621,"line_start":5949,"line_end":5949,"column_start":29,"column_end":40,"is_primary":true,"text":[{"text":"                        let mut message;","highlight_start":29,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":266610,"byte_end":266614,"line_start":5949,"line_end":5949,"column_start":29,"column_end":33,"is_primary":true,"text":[{"text":"                        let mut message;","highlight_start":29,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5949:29\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5949\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        let mut message;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `tx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\faye.rs","byte_start":7581,"byte_end":7583,"line_start":232,"line_end":232,"column_start":83,"column_end":85,"is_primary":true,"text":[{"text":"    pub async fn listen(&mut self, mut rx: UnboundedReceiver<FayeMessageRequest>, tx: UnboundedSender<FayeMessage>) -> Result<FayeMessage, Box<dyn std::error::Error>> {","highlight_start":83,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\faye.rs","byte_start":7581,"byte_end":7583,"line_start":232,"line_end":232,"column_start":83,"column_end":85,"is_primary":true,"text":[{"text":"    pub async fn listen(&mut self, mut rx: UnboundedReceiver<FayeMessageRequest>, tx: UnboundedSender<FayeMessage>) -> Result<FayeMessage, Box<dyn std::error::Error>> {","highlight_start":83,"highlight_end":85}],"label":null,"suggested_replacement":"_tx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `tx`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\faye.rs:232:83\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mdedReceiver<FayeMessageRequest>, tx: UnboundedSender<FayeMessage>) -> Result<FayeMessage, Box<dyn std::error::Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_tx`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `sender`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31845,"byte_end":31851,"line_start":798,"line_end":798,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    sender: mpsc::UnboundedSender<FileTransmitPacket>    ","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31845,"byte_end":31851,"line_start":798,"line_end":798,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    sender: mpsc::UnboundedSender<FileTransmitPacket>    ","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_sender","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `sender`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:798:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m798\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sender: mpsc::UnboundedSender<FileTransmitPacket>    \u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_sender`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `udp_file_server`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31917,"byte_end":31932,"line_start":800,"line_end":800,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"    let mut udp_file_server = app_state.udp_file_server.clone();","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31917,"byte_end":31932,"line_start":800,"line_end":800,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"    let mut udp_file_server = app_state.udp_file_server.clone();","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":"_udp_file_server","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `udp_file_server`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:800:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m800\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut udp_file_server = app_state.udp_file_server.clone();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_udp_file_server`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31913,"byte_end":31932,"line_start":800,"line_end":800,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    let mut udp_file_server = app_state.udp_file_server.clone();","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31913,"byte_end":31917,"line_start":800,"line_end":800,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut udp_file_server = app_state.udp_file_server.clone();","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:800:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m800\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut udp_file_server = app_state.udp_file_server.clone();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Digest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2258,"byte_end":2264,"line_start":54,"line_end":54,"column_start":20,"column_end":26,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest}; // 用于生成 sha2","highlight_start":20,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Digest`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:54:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sha2::{Sha256, Digest}; // 用于生成 sha2\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::io::Write`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":47,"byte_end":61,"line_start":3,"line_end":3,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::io::Write;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::io::Write`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::Write;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Read`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1426,"byte_end":1430,"line_start":38,"line_end":38,"column_start":26,"column_end":30,"is_primary":true,"text":[{"text":"use std::io::{BufReader, Read};","highlight_start":26,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Read`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:38:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m38\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::{BufReader, Read};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `msg_clone`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":19253,"byte_end":19262,"line_start":544,"line_end":544,"column_start":29,"column_end":38,"is_primary":true,"text":[{"text":"                        let msg_clone = Arc::new(msg.msg.clone());","highlight_start":29,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":19253,"byte_end":19262,"line_start":544,"line_end":544,"column_start":29,"column_end":38,"is_primary":true,"text":[{"text":"                        let msg_clone = Arc::new(msg.msg.clone());","highlight_start":29,"highlight_end":38}],"label":null,"suggested_replacement":"_msg_clone","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `msg_clone`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:544:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m544\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        let msg_clone = Arc::new(msg.msg.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_msg_clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 3 previous errors; 134 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 3 previous errors; 134 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0252, E0308.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0252, E0308.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0252`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0252`.\u001b[0m\n"}
