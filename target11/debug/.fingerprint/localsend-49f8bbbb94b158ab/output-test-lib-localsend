{"$message_type":"diagnostic","message":"unused imports: `clone` and `default`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":10,"byte_end":15,"line_start":1,"line_end":1,"column_start":11,"column_end":16,"is_primary":true,"text":[{"text":"use std::{clone, collections::HashMap, default, sync::Arc, time::{Instant, SystemTime}};","highlight_start":11,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"localsend\\src\\protos.rs","byte_start":39,"byte_end":46,"line_start":1,"line_end":1,"column_start":40,"column_end":47,"is_primary":true,"text":[{"text":"use std::{clone, collections::HashMap, default, sync::Arc, time::{Instant, SystemTime}};","highlight_start":40,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":10,"byte_end":17,"line_start":1,"line_end":1,"column_start":11,"column_end":18,"is_primary":true,"text":[{"text":"use std::{clone, collections::HashMap, default, sync::Arc, time::{Instant, SystemTime}};","highlight_start":11,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"localsend\\src\\protos.rs","byte_start":37,"byte_end":46,"line_start":1,"line_end":1,"column_start":38,"column_end":47,"is_primary":true,"text":[{"text":"use std::{clone, collections::HashMap, default, sync::Arc, time::{Instant, SystemTime}};","highlight_start":38,"highlight_end":47}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `clone` and `default`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\protos.rs:1:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{clone, collections::HashMap, default, sync::Arc, time::{Instant, SystemTime}};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `UdpSocket`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":261,"byte_end":270,"line_start":9,"line_end":9,"column_start":16,"column_end":25,"is_primary":true,"text":[{"text":"use std::net::{UdpSocket, SocketAddr};","highlight_start":16,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":261,"byte_end":272,"line_start":9,"line_end":9,"column_start":16,"column_end":27,"is_primary":true,"text":[{"text":"use std::net::{UdpSocket, SocketAddr};","highlight_start":16,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"localsend\\src\\protos.rs","byte_start":260,"byte_end":261,"line_start":9,"line_end":9,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"use std::net::{UdpSocket, SocketAddr};","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"localsend\\src\\protos.rs","byte_start":282,"byte_end":283,"line_start":9,"line_end":9,"column_start":37,"column_end":38,"is_primary":true,"text":[{"text":"use std::net::{UdpSocket, SocketAddr};","highlight_start":37,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UdpSocket`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\protos.rs:9:16\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::net::{UdpSocket, SocketAddr};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Rng` and `rngs::ThreadRng`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":303,"byte_end":318,"line_start":10,"line_end":10,"column_start":18,"column_end":33,"is_primary":true,"text":[{"text":"use rand::{self, rngs::ThreadRng, Rng};","highlight_start":18,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"localsend\\src\\protos.rs","byte_start":320,"byte_end":323,"line_start":10,"line_end":10,"column_start":35,"column_end":38,"is_primary":true,"text":[{"text":"use rand::{self, rngs::ThreadRng, Rng};","highlight_start":35,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":301,"byte_end":323,"line_start":10,"line_end":10,"column_start":16,"column_end":38,"is_primary":true,"text":[{"text":"use rand::{self, rngs::ThreadRng, Rng};","highlight_start":16,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Rng` and `rngs::ThreadRng`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\protos.rs:10:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand::{self, rngs::ThreadRng, Rng};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::thread`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":331,"byte_end":342,"line_start":11,"line_end":11,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":327,"byte_end":345,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":1,"highlight_end":17},{"text":"use actix::prelude::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::thread`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\protos.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::thread;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `actix::prelude::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":349,"byte_end":366,"line_start":12,"line_end":12,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"use actix::prelude::*;","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":345,"byte_end":369,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use actix::prelude::*;","highlight_start":1,"highlight_end":23},{"text":"// use std::sync::{Arc, Mutex};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `actix::prelude::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\protos.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse actix::prelude::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CertificateParams`, `Certificate`, `DnType`, and `DnValue`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\utils.rs","byte_start":105,"byte_end":116,"line_start":4,"line_end":4,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use rcgen::{Certificate, CertificateParams, DnType, DnValue};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"localsend\\src\\utils.rs","byte_start":118,"byte_end":135,"line_start":4,"line_end":4,"column_start":26,"column_end":43,"is_primary":true,"text":[{"text":"use rcgen::{Certificate, CertificateParams, DnType, DnValue};","highlight_start":26,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"localsend\\src\\utils.rs","byte_start":137,"byte_end":143,"line_start":4,"line_end":4,"column_start":45,"column_end":51,"is_primary":true,"text":[{"text":"use rcgen::{Certificate, CertificateParams, DnType, DnValue};","highlight_start":45,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"localsend\\src\\utils.rs","byte_start":145,"byte_end":152,"line_start":4,"line_end":4,"column_start":53,"column_end":60,"is_primary":true,"text":[{"text":"use rcgen::{Certificate, CertificateParams, DnType, DnValue};","highlight_start":53,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\utils.rs","byte_start":93,"byte_end":156,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rcgen::{Certificate, CertificateParams, DnType, DnValue};","highlight_start":1,"highlight_end":62},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `CertificateParams`, `Certificate`, `DnType`, and `DnValue`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\utils.rs:4:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rcgen::{Certificate, CertificateParams, DnType, DnValue};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `RwLock`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":140,"byte_end":146,"line_start":2,"line_end":2,"column_start":129,"column_end":135,"is_primary":true,"text":[{"text":"    collections::HashMap, fmt, fs::{self, File}, io::Read, net::{IpAddr, Ipv4Addr}, sync::{atomic::{AtomicBool, Ordering}, Arc, RwLock}, time::{Duration, SystemTime}","highlight_start":129,"highlight_end":135}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":138,"byte_end":146,"line_start":2,"line_end":2,"column_start":127,"column_end":135,"is_primary":true,"text":[{"text":"    collections::HashMap, fmt, fs::{self, File}, io::Read, net::{IpAddr, Ipv4Addr}, sync::{atomic::{AtomicBool, Ordering}, Arc, RwLock}, time::{Duration, SystemTime}","highlight_start":127,"highlight_end":135}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `RwLock`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\server.rs:2:129\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    collections::HashMap, fmt, fs::{self, File}, io::Read, net::{IpAddr, Ipv4Addr}, sync::{atomic::{AtomicBool, Ordering}, Arc, RwLock}, \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                                                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `log::info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":189,"byte_end":198,"line_start":5,"line_end":5,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use log::info;","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":185,"byte_end":201,"line_start":5,"line_end":6,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use log::info;","highlight_start":1,"highlight_end":15},{"text":"use network_interface::Addr;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `log::info`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\server.rs:5:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::info;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `network_interface::Addr`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":205,"byte_end":228,"line_start":6,"line_end":6,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use network_interface::Addr;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":201,"byte_end":231,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use network_interface::Addr;","highlight_start":1,"highlight_end":29},{"text":"use ring::digest;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `network_interface::Addr`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\server.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse network_interface::Addr;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tracing::debug`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":433,"byte_end":447,"line_start":10,"line_end":10,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use tracing::debug;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":429,"byte_end":450,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tracing::debug;","highlight_start":1,"highlight_end":20},{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tracing::debug`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\server.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::debug;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `MAX_RETRANSMIT`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":802,"byte_end":816,"line_start":16,"line_end":16,"column_start":192,"column_end":206,"is_primary":true,"text":[{"text":"    protos::{  FileTransmitPacket, Task, TaskState, UDPMessageInfo, UDPMessageType, UDPResponse}, utils::get_device_ip_addr, CHUNK_SIZE, FILE_MESSAGE_BUFFER_SIZE, HASH_SIZE, MAX_PACKET_SIZE, MAX_RETRANSMIT, NUM_REPEAT","highlight_start":192,"highlight_end":206}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":800,"byte_end":816,"line_start":16,"line_end":16,"column_start":190,"column_end":206,"is_primary":true,"text":[{"text":"    protos::{  FileTransmitPacket, Task, TaskState, UDPMessageInfo, UDPMessageType, UDPResponse}, utils::get_device_ip_addr, CHUNK_SIZE, FILE_MESSAGE_BUFFER_SIZE, HASH_SIZE, MAX_PACKET_SIZE, MAX_RETRANSMIT, NUM_REPEAT","highlight_start":190,"highlight_end":206}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `MAX_RETRANSMIT`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\server.rs:16:192\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mSIZE, FILE_MESSAGE_BUFFER_SIZE, HASH_SIZE, MAX_PACKET_SIZE, MAX_RETRANSMIT, NUM_REPEAT\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `receiver`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":5580,"byte_end":5588,"line_start":156,"line_end":156,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        receiver: Arc<Mutex<mpsc::UnboundedReceiver<FileTransmitPacket>>>,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":5580,"byte_end":5588,"line_start":156,"line_end":156,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        receiver: Arc<Mutex<mpsc::UnboundedReceiver<FileTransmitPacket>>>,","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_receiver","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `receiver`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\server.rs:156:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m156\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        receiver: Arc<Mutex<mpsc::UnboundedReceiver<FileTransmitPacket>>>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_receiver`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":10345,"byte_end":10365,"line_start":258,"line_end":258,"column_start":25,"column_end":45,"is_primary":true,"text":[{"text":"                    let mut udp_server_clone = self.clone();","highlight_start":25,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":10345,"byte_end":10349,"line_start":258,"line_end":258,"column_start":25,"column_end":29,"is_primary":true,"text":[{"text":"                    let mut udp_server_clone = self.clone();","highlight_start":25,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\server.rs:258:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m258\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let mut udp_server_clone = self.clone();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `file_size`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":26555,"byte_end":26564,"line_start":550,"line_end":550,"column_start":21,"column_end":30,"is_primary":true,"text":[{"text":"                let file_size = fs::metadata(&dir_path);","highlight_start":21,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"localsend\\src\\server.rs","byte_start":26555,"byte_end":26564,"line_start":550,"line_end":550,"column_start":21,"column_end":30,"is_primary":true,"text":[{"text":"                let file_size = fs::metadata(&dir_path);","highlight_start":21,"highlight_end":30}],"label":null,"suggested_replacement":"_file_size","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `file_size`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\server.rs:550:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m550\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let file_size = fs::metadata(&dir_path);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_file_size`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `MAX_RETRANSMIT` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\lib.rs","byte_start":419,"byte_end":433,"line_start":16,"line_end":16,"column_start":7,"column_end":21,"is_primary":true,"text":[{"text":"const MAX_RETRANSMIT: i32 = 1; //重传次数","highlight_start":7,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: constant `MAX_RETRANSMIT` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\lib.rs:16:7\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mconst MAX_RETRANSMIT: i32 = 1; //重传次数\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `server_tx`, `client_rx`, and `receive_session` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"localsend\\src\\protos.rs","byte_start":5102,"byte_end":5110,"line_start":170,"line_end":170,"column_start":12,"column_end":20,"is_primary":false,"text":[{"text":"pub struct AppState {","highlight_start":12,"highlight_end":20}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"localsend\\src\\protos.rs","byte_start":5129,"byte_end":5138,"line_start":171,"line_end":171,"column_start":16,"column_end":25,"is_primary":true,"text":[{"text":"    pub(crate) server_tx: Sender<ServerMessage>, // 服务器消息发送者","highlight_start":16,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"localsend\\src\\protos.rs","byte_start":5207,"byte_end":5216,"line_start":172,"line_end":172,"column_start":16,"column_end":25,"is_primary":true,"text":[{"text":"    pub(crate) client_rx: Receiver<ClientMessage>, // 客户端消息接收者","highlight_start":16,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"localsend\\src\\protos.rs","byte_start":5287,"byte_end":5302,"line_start":173,"line_end":173,"column_start":16,"column_end":31,"is_primary":true,"text":[{"text":"    pub(crate) receive_session: Option<ReceiveSession>, // 接收会话","highlight_start":16,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `server_tx`, `client_rx`, and `receive_session` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mlocalsend\\src\\protos.rs:171:16\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m170\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct AppState {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) server_tx: Sender<ServerMessage>, // 服务器消息发送者\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) client_rx: Receiver<ClientMessage>, // 客户端消息接收者\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub(crate) receive_session: Option<ReceiveSession>, // 接收会话\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"16 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 16 warnings emitted\u001b[0m\n\n"}
