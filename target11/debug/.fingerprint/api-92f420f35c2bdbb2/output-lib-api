{"$message_type":"diagnostic","message":"unused import: `console::style`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":178,"byte_end":192,"line_start":6,"line_end":6,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use console::style;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":174,"byte_end":195,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use console::style;","highlight_start":1,"highlight_end":20},{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `console::style`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse console::style;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `FileInfo`, `ServerMessage`, and `TaskState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":219,"byte_end":227,"line_start":7,"line_end":7,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":249,"byte_end":262,"line_start":7,"line_end":7,"column_start":55,"column_end":68,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":55,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":264,"byte_end":273,"line_start":7,"line_end":7,"column_start":70,"column_end":79,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":70,"highlight_end":79}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":219,"byte_end":229,"line_start":7,"line_end":7,"column_start":25,"column_end":35,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":25,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":247,"byte_end":273,"line_start":7,"line_end":7,"column_start":53,"column_end":79,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":53,"highlight_end":79}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":218,"byte_end":219,"line_start":7,"line_end":7,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":273,"byte_end":274,"line_start":7,"line_end":7,"column_start":79,"column_end":80,"is_primary":true,"text":[{"text":"use localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};","highlight_start":79,"highlight_end":80}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `FileInfo`, `ServerMessage`, and `TaskState`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:7:25\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse localsend::protos::{FileInfo, FileTransmitPacket, ServerMessage, TaskState};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `log::kv::Value`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":317,"byte_end":331,"line_start":9,"line_end":9,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use log::kv::Value;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":313,"byte_end":334,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use log::kv::Value;","highlight_start":1,"highlight_end":20},{"text":"use rand::Rng;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `log::kv::Value`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::kv::Value;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CopyOptions as CopyDirOption` and `copy as copy_dir`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":589,"byte_end":605,"line_start":17,"line_end":17,"column_start":21,"column_end":37,"is_primary":true,"text":[{"text":"use fs_extra::dir::{copy as copy_dir, CopyOptions as CopyDirOption};","highlight_start":21,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":607,"byte_end":635,"line_start":17,"line_end":17,"column_start":39,"column_end":67,"is_primary":true,"text":[{"text":"use fs_extra::dir::{copy as copy_dir, CopyOptions as CopyDirOption};","highlight_start":39,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":569,"byte_end":639,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use fs_extra::dir::{copy as copy_dir, CopyOptions as CopyDirOption};","highlight_start":1,"highlight_end":69},{"text":"use actix_session::{SessionMiddleware, storage::CookieSessionStore};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `CopyOptions as CopyDirOption` and `copy as copy_dir`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:17:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fs_extra::dir::{copy as copy_dir, CopyOptions as CopyDirOption};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::spawn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":748,"byte_end":760,"line_start":20,"line_end":20,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"use tokio::spawn;","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":744,"byte_end":763,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::spawn;","highlight_start":1,"highlight_end":18},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tokio::spawn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:20:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::spawn;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `runtime`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":777,"byte_end":784,"line_start":22,"line_end":22,"column_start":13,"column_end":20,"is_primary":true,"text":[{"text":"use tokio::{runtime, sync::mpsc};","highlight_start":13,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":777,"byte_end":786,"line_start":22,"line_end":22,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"use tokio::{runtime, sync::mpsc};","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":776,"byte_end":777,"line_start":22,"line_end":22,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use tokio::{runtime, sync::mpsc};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":796,"byte_end":797,"line_start":22,"line_end":22,"column_start":32,"column_end":33,"is_primary":true,"text":[{"text":"use tokio::{runtime, sync::mpsc};","highlight_start":32,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `runtime`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:22:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::{runtime, sync::mpsc};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `MultiProgress`, `ProgressBar`, `ProgressState`, and `ProgressStyle`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":816,"byte_end":829,"line_start":23,"line_end":23,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":831,"byte_end":842,"line_start":23,"line_end":23,"column_start":32,"column_end":43,"is_primary":true,"text":[{"text":"use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};","highlight_start":32,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":844,"byte_end":857,"line_start":23,"line_end":23,"column_start":45,"column_end":58,"is_primary":true,"text":[{"text":"use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};","highlight_start":45,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":859,"byte_end":872,"line_start":23,"line_end":23,"column_start":60,"column_end":73,"is_primary":true,"text":[{"text":"use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};","highlight_start":60,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":800,"byte_end":876,"line_start":23,"line_end":24,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};","highlight_start":1,"highlight_end":75},{"text":"use tracing::{debug, info};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `MultiProgress`, `ProgressBar`, `ProgressState`, and `ProgressStyle`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:23:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse indicatif::{MultiProgress, ProgressBar, ProgressState, ProgressStyle};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `debug` and `info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":890,"byte_end":895,"line_start":24,"line_end":24,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{debug, info};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":897,"byte_end":901,"line_start":24,"line_end":24,"column_start":22,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{debug, info};","highlight_start":22,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":876,"byte_end":905,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tracing::{debug, info};","highlight_start":1,"highlight_end":28},{"text":"use dialoguer::{theme::ColorfulTheme, MultiSelect};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `debug` and `info`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:24:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, info};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `MultiSelect` and `theme::ColorfulTheme`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":921,"byte_end":941,"line_start":25,"line_end":25,"column_start":17,"column_end":37,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, MultiSelect};","highlight_start":17,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":943,"byte_end":954,"line_start":25,"line_end":25,"column_start":39,"column_end":50,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, MultiSelect};","highlight_start":39,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":905,"byte_end":958,"line_start":25,"line_end":26,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, MultiSelect};","highlight_start":1,"highlight_end":52},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `MultiSelect` and `theme::ColorfulTheme`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:25:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse dialoguer::{theme::ColorfulTheme, MultiSelect};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `thread`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":1435,"byte_end":1441,"line_start":45,"line_end":45,"column_start":16,"column_end":22,"is_primary":true,"text":[{"text":"use std::{env, thread};","highlight_start":16,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":1433,"byte_end":1441,"line_start":45,"line_end":45,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use std::{env, thread};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":1429,"byte_end":1430,"line_start":45,"line_end":45,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use std::{env, thread};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":1441,"byte_end":1442,"line_start":45,"line_end":45,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use std::{env, thread};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `thread`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:45:16\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m45\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{env, thread};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `BusinessError` and `BusinessResponse`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":1544,"byte_end":1557,"line_start":48,"line_end":48,"column_start":25,"column_end":38,"is_primary":true,"text":[{"text":"use context::{AppState, BusinessError, BusinessResponse, HttpFileCache};","highlight_start":25,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\lib.rs","byte_start":1559,"byte_end":1575,"line_start":48,"line_end":48,"column_start":40,"column_end":56,"is_primary":true,"text":[{"text":"use context::{AppState, BusinessError, BusinessResponse, HttpFileCache};","highlight_start":40,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":1542,"byte_end":1575,"line_start":48,"line_end":48,"column_start":23,"column_end":56,"is_primary":true,"text":[{"text":"use context::{AppState, BusinessError, BusinessResponse, HttpFileCache};","highlight_start":23,"highlight_end":56}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `BusinessError` and `BusinessResponse`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:48:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse context::{AppState, BusinessError, BusinessResponse, HttpFileCache};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `HashSet` and `fs`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":33,"byte_end":40,"line_start":1,"line_end":1,"column_start":34,"column_end":41,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":34,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":48,"byte_end":50,"line_start":1,"line_end":1,"column_start":49,"column_end":51,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":49,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":31,"byte_end":40,"line_start":1,"line_end":1,"column_start":32,"column_end":41,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":32,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":23,"byte_end":24,"line_start":1,"line_end":1,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":40,"byte_end":41,"line_start":1,"line_end":1,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":46,"byte_end":50,"line_start":1,"line_end":1,"column_start":47,"column_end":51,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, env, fs};","highlight_start":47,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `HashSet` and `fs`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_web.rs:1:34\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{collections::{HashMap, HashSet}, env, fs};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `put`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":153,"byte_end":156,"line_start":7,"line_end":7,"column_start":16,"column_end":19,"is_primary":true,"text":[{"text":"    get, post, put, web, HttpRequest, HttpResponse, Result","highlight_start":16,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":151,"byte_end":156,"line_start":7,"line_end":7,"column_start":14,"column_end":19,"is_primary":true,"text":[{"text":"    get, post, put, web, HttpRequest, HttpResponse, Result","highlight_start":14,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `put`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_web.rs:7:16\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    get, post, put, web, HttpRequest, HttpResponse, Result\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":589,"byte_end":593,"line_start":15,"line_end":15,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":589,"byte_end":595,"line_start":15,"line_end":15,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":588,"byte_end":589,"line_start":15,"line_end":15,"column_start":17,"column_end":18,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":17,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_web.rs","byte_start":600,"byte_end":601,"line_start":15,"line_end":15,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `json`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_web.rs:15:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json::{json, Value};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Cursor`, `cell`, and `thread::current`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":26,"byte_end":30,"line_start":2,"line_end":2,"column_start":11,"column_end":15,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":11,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":74,"byte_end":80,"line_start":2,"line_end":2,"column_start":59,"column_end":65,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":59,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":133,"byte_end":148,"line_start":2,"line_end":2,"column_start":118,"column_end":133,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":118,"highlight_end":133}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":26,"byte_end":32,"line_start":2,"line_end":2,"column_start":11,"column_end":17,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":11,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":74,"byte_end":82,"line_start":2,"line_end":2,"column_start":59,"column_end":67,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":59,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":73,"byte_end":74,"line_start":2,"line_end":2,"column_start":58,"column_end":59,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":58,"highlight_end":59}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":90,"byte_end":91,"line_start":2,"line_end":2,"column_start":75,"column_end":76,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":75,"highlight_end":76}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":131,"byte_end":148,"line_start":2,"line_end":2,"column_start":116,"column_end":133,"is_primary":true,"text":[{"text":"use std::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};","highlight_start":116,"highlight_end":133}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Cursor`, `cell`, and `thread::current`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:2:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m::{cell, collections::{HashMap, HashSet}, fs, io::{Cursor, SeekFrom}, path::Path, result, sync::{Arc, Mutex}, thread::current};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `course`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":376,"byte_end":382,"line_start":9,"line_end":9,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"use entity::{course, ","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":376,"byte_end":390,"line_start":9,"line_end":10,"column_start":14,"column_end":5,"is_primary":true,"text":[{"text":"use entity::{course, ","highlight_start":14,"highlight_end":22},{"text":"    section_record::SectionRecordItem, ","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `course`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:9:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse entity::{course, \u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `file`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":945,"byte_end":949,"line_start":21,"line_end":21,"column_start":16,"column_end":20,"is_primary":true,"text":[{"text":"use fs_extra::{file, remove_items};","highlight_start":16,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":945,"byte_end":951,"line_start":21,"line_end":21,"column_start":16,"column_end":22,"is_primary":true,"text":[{"text":"use fs_extra::{file, remove_items};","highlight_start":16,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":944,"byte_end":945,"line_start":21,"line_end":21,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"use fs_extra::{file, remove_items};","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":963,"byte_end":964,"line_start":21,"line_end":21,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use fs_extra::{file, remove_items};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `file`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:21:16\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fs_extra::{file, remove_items};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Reader` and `events::Event`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1085,"byte_end":1098,"line_start":25,"line_end":25,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"use quick_xml::{events::Event, Reader};","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1100,"byte_end":1106,"line_start":25,"line_end":25,"column_start":32,"column_end":38,"is_primary":true,"text":[{"text":"use quick_xml::{events::Event, Reader};","highlight_start":32,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1069,"byte_end":1110,"line_start":25,"line_end":26,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use quick_xml::{events::Event, Reader};","highlight_start":1,"highlight_end":40},{"text":"use sea_orm::{prelude::{Decimal, Json}, sqlx::types::uuid, IsolationLevel, TransactionTrait};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Reader` and `events::Event`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:25:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse quick_xml::{events::Event, Reader};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `sqlx::types::uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1150,"byte_end":1167,"line_start":26,"line_end":26,"column_start":41,"column_end":58,"is_primary":true,"text":[{"text":"use sea_orm::{prelude::{Decimal, Json}, sqlx::types::uuid, IsolationLevel, TransactionTrait};","highlight_start":41,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1148,"byte_end":1167,"line_start":26,"line_end":26,"column_start":39,"column_end":58,"is_primary":true,"text":[{"text":"use sea_orm::{prelude::{Decimal, Json}, sqlx::types::uuid, IsolationLevel, TransactionTrait};","highlight_start":39,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `sqlx::types::uuid`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:26:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sea_orm::{prelude::{Decimal, Json}, sqlx::types::uuid, IsolationLevel, TransactionTra\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `format`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1364,"byte_end":1370,"line_start":34,"line_end":34,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"use chrono::{format, prelude::*};","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1364,"byte_end":1372,"line_start":34,"line_end":34,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use chrono::{format, prelude::*};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1363,"byte_end":1364,"line_start":34,"line_end":34,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use chrono::{format, prelude::*};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1382,"byte_end":1383,"line_start":34,"line_end":34,"column_start":32,"column_end":33,"is_primary":true,"text":[{"text":"use chrono::{format, prelude::*};","highlight_start":32,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `format`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:34:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m34\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{format, prelude::*};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ListOnlineSessionCount`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1440,"byte_end":1462,"line_start":35,"line_end":35,"column_start":55,"column_end":77,"is_primary":true,"text":[{"text":"use websocket::server::{ClientMessage, ListOnlineIPs, ListOnlineSessionCount};","highlight_start":55,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1438,"byte_end":1462,"line_start":35,"line_end":35,"column_start":53,"column_end":77,"is_primary":true,"text":[{"text":"use websocket::server::{ClientMessage, ListOnlineIPs, ListOnlineSessionCount};","highlight_start":53,"highlight_end":77}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `ListOnlineSessionCount`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:35:55\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse websocket::server::{ClientMessage, ListOnlineIPs, ListOnlineSessionCount};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `create_course_sections`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1510,"byte_end":1532,"line_start":36,"line_end":36,"column_start":45,"column_end":67,"is_primary":true,"text":[{"text":"use crate::{context::ProgressData, course::{create_course_sections, get_course_path, OJContent}};","highlight_start":45,"highlight_end":67}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1510,"byte_end":1534,"line_start":36,"line_end":36,"column_start":45,"column_end":69,"is_primary":true,"text":[{"text":"use crate::{context::ProgressData, course::{create_course_sections, get_course_path, OJContent}};","highlight_start":45,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `create_course_sections`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:36:45\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::{context::ProgressData, course::{create_course_sections, get_course_path, OJCo\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `OIData`, `post_course_download_course_zip`, and `write_log`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1747,"byte_end":1778,"line_start":39,"line_end":39,"column_start":113,"column_end":144,"is_primary":true,"text":[{"text":"use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };","highlight_start":113,"highlight_end":144}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1780,"byte_end":1789,"line_start":39,"line_end":39,"column_start":146,"column_end":155,"is_primary":true,"text":[{"text":"use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };","highlight_start":146,"highlight_end":155}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1801,"byte_end":1807,"line_start":39,"line_end":39,"column_start":167,"column_end":173,"is_primary":true,"text":[{"text":"use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };","highlight_start":167,"highlight_end":173}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":1745,"byte_end":1789,"line_start":39,"line_end":39,"column_start":111,"column_end":155,"is_primary":true,"text":[{"text":"use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };","highlight_start":111,"highlight_end":155}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":1799,"byte_end":1807,"line_start":39,"line_end":39,"column_start":165,"column_end":173,"is_primary":true,"text":[{"text":"use service::{ extract_zip, get_client, get_client_csrf_token_from_session, get_path_in_exe_dir, http_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Question, Remote, RemoteSession, DOWNLOAD_TIME_OUT };","highlight_start":165,"highlight_end":173}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `OIData`, `post_course_download_course_zip`, and `write_log`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:39:113\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mtp_get_file, post_course_download_course_zip, write_log, Mutation, OIData, Query, Ques\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `form::json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2094,"byte_end":2104,"line_start":48,"line_end":48,"column_start":23,"column_end":33,"is_primary":true,"text":[{"text":"use actix_multipart::{form::json, Multipart};","highlight_start":23,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2094,"byte_end":2106,"line_start":48,"line_end":48,"column_start":23,"column_end":35,"is_primary":true,"text":[{"text":"use actix_multipart::{form::json, Multipart};","highlight_start":23,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":2093,"byte_end":2094,"line_start":48,"line_end":48,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use actix_multipart::{form::json, Multipart};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":2115,"byte_end":2116,"line_start":48,"line_end":48,"column_start":44,"column_end":45,"is_primary":true,"text":[{"text":"use actix_multipart::{form::json, Multipart};","highlight_start":44,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `form::json`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:48:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse actix_multipart::{form::json, Multipart};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `join`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2133,"byte_end":2137,"line_start":49,"line_end":49,"column_start":15,"column_end":19,"is_primary":true,"text":[{"text":"use futures::{join, StreamExt, TryStreamExt};","highlight_start":15,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2133,"byte_end":2139,"line_start":49,"line_end":49,"column_start":15,"column_end":21,"is_primary":true,"text":[{"text":"use futures::{join, StreamExt, TryStreamExt};","highlight_start":15,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `join`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:49:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse futures::{join, StreamExt, TryStreamExt};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rand::Rng`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2202,"byte_end":2211,"line_start":53,"line_end":53,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"use rand::Rng; // 用于生成随机数","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2198,"byte_end":2212,"line_start":53,"line_end":53,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"use rand::Rng; // 用于生成随机数","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `rand::Rng`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:53:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand::Rng; // 用于生成随机数\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Sha256`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2250,"byte_end":2256,"line_start":54,"line_end":54,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest}; // 用于生成 sha2","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2250,"byte_end":2258,"line_start":54,"line_end":54,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest}; // 用于生成 sha2","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":2249,"byte_end":2250,"line_start":54,"line_end":54,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest}; // 用于生成 sha2","highlight_start":11,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":2264,"byte_end":2265,"line_start":54,"line_end":54,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest}; // 用于生成 sha2","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Sha256`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:54:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sha2::{Sha256, Digest}; // 用于生成 sha2\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::fmt::format`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":4,"byte_end":20,"line_start":1,"line_end":1,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"use std::fmt::format;","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":0,"byte_end":23,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::fmt::format;","highlight_start":1,"highlight_end":22},{"text":"use std::fs::File;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::fmt::format`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::fmt::format;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::fs::File`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":27,"byte_end":40,"line_start":2,"line_end":2,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"use std::fs::File;","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":23,"byte_end":43,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::fs::File;","highlight_start":1,"highlight_end":19},{"text":"use std::io::Write;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::fs::File`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::fs::File;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::os::windows::ffi::OsStrExt`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":68,"byte_end":99,"line_start":4,"line_end":4,"column_start":5,"column_end":36,"is_primary":true,"text":[{"text":"use std::os::windows::ffi::OsStrExt;","highlight_start":5,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":64,"byte_end":102,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::os::windows::ffi::OsStrExt;","highlight_start":1,"highlight_end":37},{"text":"use std::os::windows::process::CommandExt;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::os::windows::ffi::OsStrExt`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::os::windows::ffi::OsStrExt;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::time::Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":150,"byte_end":169,"line_start":6,"line_end":6,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":146,"byte_end":172,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":25},{"text":"use std::thread;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::time::Duration`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:6:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::Duration;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::thread`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":176,"byte_end":187,"line_start":7,"line_end":7,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":172,"byte_end":190,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::thread;","highlight_start":1,"highlight_end":17},{"text":"use std::{collections::HashMap, fs, process::Command};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::thread`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::thread;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `mount_virtual_disk` and `unmount_virtual_disk`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":506,"byte_end":524,"line_start":14,"line_end":14,"column_start":127,"column_end":145,"is_primary":true,"text":[{"text":"use service::{copy_recursively, extract_zip, get_client, get_path_in_exe_dir, get_system_version, http_get_file, log_request, mount_virtual_disk, unmount_virtual_disk, write_log, DOWNLOAD_TIME_OUT};","highlight_start":127,"highlight_end":145}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":526,"byte_end":546,"line_start":14,"line_end":14,"column_start":147,"column_end":167,"is_primary":true,"text":[{"text":"use service::{copy_recursively, extract_zip, get_client, get_path_in_exe_dir, get_system_version, http_get_file, log_request, mount_virtual_disk, unmount_virtual_disk, write_log, DOWNLOAD_TIME_OUT};","highlight_start":147,"highlight_end":167}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":504,"byte_end":546,"line_start":14,"line_end":14,"column_start":125,"column_end":167,"is_primary":true,"text":[{"text":"use service::{copy_recursively, extract_zip, get_client, get_path_in_exe_dir, get_system_version, http_get_file, log_request, mount_virtual_disk, unmount_virtual_disk, write_log, DOWNLOAD_TIME_OUT};","highlight_start":125,"highlight_end":167}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `mount_virtual_disk` and `unmount_virtual_disk`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:14:127\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mget_file, log_request, mount_virtual_disk, unmount_virtual_disk, write_log, DOWNLOAD_T\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `fs_extra::dir::remove`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":584,"byte_end":605,"line_start":15,"line_end":15,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use fs_extra::dir::remove;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":580,"byte_end":608,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use fs_extra::dir::remove;","highlight_start":1,"highlight_end":27},{"text":"use serde_json::Value;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `fs_extra::dir::remove`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse fs_extra::dir::remove;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `FindWindowW`, `HWND_TOPMOST`, `SWP_NOMOVE`, `SWP_NOSIZE`, and `SetWindowPos`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":897,"byte_end":908,"line_start":24,"line_end":24,"column_start":27,"column_end":38,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":27,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":910,"byte_end":922,"line_start":24,"line_end":24,"column_start":40,"column_end":52,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":40,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":924,"byte_end":936,"line_start":24,"line_end":24,"column_start":54,"column_end":66,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":54,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":938,"byte_end":948,"line_start":24,"line_end":24,"column_start":68,"column_end":78,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":68,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":950,"byte_end":960,"line_start":24,"line_end":24,"column_start":80,"column_end":90,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":80,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":871,"byte_end":964,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};","highlight_start":1,"highlight_end":92},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `FindWindowW`, `HWND_TOPMOST`, `SWP_NOMOVE`, `SWP_NOSIZE`, and `SetWindowPos`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:24:27\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse winapi::um::winuser::{FindWindowW, SetWindowPos, HWND_TOPMOST, SWP_NOMOVE, SWP_NOSIZE};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `task` and `time`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":1061,"byte_end":1065,"line_start":30,"line_end":30,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"use tokio::{io, task, time};","highlight_start":17,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":1067,"byte_end":1071,"line_start":30,"line_end":30,"column_start":23,"column_end":27,"is_primary":true,"text":[{"text":"use tokio::{io, task, time};","highlight_start":23,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":1059,"byte_end":1071,"line_start":30,"line_end":30,"column_start":15,"column_end":27,"is_primary":true,"text":[{"text":"use tokio::{io, task, time};","highlight_start":15,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":1056,"byte_end":1057,"line_start":30,"line_end":30,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use tokio::{io, task, time};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":1071,"byte_end":1072,"line_start":30,"line_end":30,"column_start":27,"column_end":28,"is_primary":true,"text":[{"text":"use tokio::{io, task, time};","highlight_start":27,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `task` and `time`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:30:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::{io, task, time};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Cursor`, `HashSet`, `Instant`, `Stdio`, and `path::PathBuf`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":49,"byte_end":56,"line_start":2,"line_end":2,"column_start":34,"column_end":41,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":34,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":69,"byte_end":75,"line_start":2,"line_end":2,"column_start":54,"column_end":60,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":54,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":85,"byte_end":98,"line_start":2,"line_end":2,"column_start":70,"column_end":83,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":70,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":120,"byte_end":125,"line_start":2,"line_end":2,"column_start":105,"column_end":110,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":105,"highlight_end":110}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":145,"byte_end":152,"line_start":2,"line_end":2,"column_start":130,"column_end":137,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":130,"highlight_end":137}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":47,"byte_end":56,"line_start":2,"line_end":2,"column_start":32,"column_end":41,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":32,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":39,"byte_end":40,"line_start":2,"line_end":2,"column_start":24,"column_end":25,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":24,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":56,"byte_end":57,"line_start":2,"line_end":2,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":69,"byte_end":77,"line_start":2,"line_end":2,"column_start":54,"column_end":62,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":54,"highlight_end":62}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":67,"byte_end":69,"line_start":2,"line_end":2,"column_start":52,"column_end":54,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":52,"highlight_end":54}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":82,"byte_end":83,"line_start":2,"line_end":2,"column_start":67,"column_end":68,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":67,"highlight_end":68}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":83,"byte_end":98,"line_start":2,"line_end":2,"column_start":68,"column_end":83,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":68,"highlight_end":83}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":117,"byte_end":125,"line_start":2,"line_end":2,"column_start":102,"column_end":110,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":102,"highlight_end":110}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":109,"byte_end":110,"line_start":2,"line_end":2,"column_start":94,"column_end":95,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":94,"highlight_end":95}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":125,"byte_end":126,"line_start":2,"line_end":2,"column_start":110,"column_end":111,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":110,"highlight_end":111}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":143,"byte_end":152,"line_start":2,"line_end":2,"column_start":128,"column_end":137,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":128,"highlight_end":137}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":134,"byte_end":135,"line_start":2,"line_end":2,"column_start":119,"column_end":120,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":119,"highlight_end":120}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":152,"byte_end":153,"line_start":2,"line_end":2,"column_start":137,"column_end":138,"is_primary":true,"text":[{"text":"use std::{collections::{HashMap, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};","highlight_start":137,"highlight_end":138}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Cursor`, `HashSet`, `Instant`, `Stdio`, and `path::PathBuf`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2:34\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mp, HashSet}, fs, io::{ Cursor, Write}, path::PathBuf, process::{Command,  Stdio}, time::{Duration, Instant}};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Context`, `JsValue`, `json`, `object::JsObject`, `object::Object`, and `property::Attribute`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":380,"byte_end":384,"line_start":11,"line_end":11,"column_start":22,"column_end":26,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":22,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":386,"byte_end":400,"line_start":11,"line_end":11,"column_start":28,"column_end":42,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":28,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":403,"byte_end":419,"line_start":11,"line_end":11,"column_start":45,"column_end":61,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":45,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":421,"byte_end":440,"line_start":11,"line_end":11,"column_start":63,"column_end":82,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":63,"highlight_end":82}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":442,"byte_end":449,"line_start":11,"line_end":11,"column_start":84,"column_end":91,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":84,"highlight_end":91}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":451,"byte_end":458,"line_start":11,"line_end":11,"column_start":93,"column_end":100,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":93,"highlight_end":100}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":359,"byte_end":462,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use boa::{builtins::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};","highlight_start":1,"highlight_end":102},{"text":"// use rquickjs::{Context, Runtime, Value};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Context`, `JsValue`, `json`, `object::JsObject`, `object::Object`, and `property::Attribute`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:11:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0ms::{json, object::Object}, object::JsObject, property::Attribute, Context, JsValue};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `self`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":531,"byte_end":535,"line_start":14,"line_end":14,"column_start":23,"column_end":27,"is_primary":true,"text":[{"text":"use entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::SectionRecordItem};","highlight_start":23,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":531,"byte_end":538,"line_start":14,"line_end":14,"column_start":23,"column_end":30,"is_primary":true,"text":[{"text":"use entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::SectionRecordItem};","highlight_start":23,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":530,"byte_end":531,"line_start":14,"line_end":14,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::SectionRecordItem};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"api\\src\\course.rs","byte_start":550,"byte_end":551,"line_start":14,"line_end":14,"column_start":42,"column_end":43,"is_primary":true,"text":[{"text":"use entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::SectionRecordItem};","highlight_start":42,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `self`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:14:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse entity::{course::{self,  UploadCourse}, section::SectionItem, section_record::Section\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `log::kv::source`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":691,"byte_end":706,"line_start":16,"line_end":16,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"use log::kv::source;","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":687,"byte_end":709,"line_start":16,"line_end":17,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use log::kv::source;","highlight_start":1,"highlight_end":21},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `log::kv::source`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:16:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::kv::source;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::controller_admin`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":995,"byte_end":1018,"line_start":29,"line_end":29,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use crate::controller_admin;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":991,"byte_end":1021,"line_start":29,"line_end":30,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::controller_admin;","highlight_start":1,"highlight_end":29},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::controller_admin`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:29:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::controller_admin;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::io::BufReader`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1316,"byte_end":1334,"line_start":39,"line_end":39,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"use std::io::BufReader;","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1312,"byte_end":1337,"line_start":39,"line_end":40,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::io::BufReader;","highlight_start":1,"highlight_end":24},{"text":"use quick_xml::events::Event;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::io::BufReader`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:39:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::BufReader;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `quick_xml::events::Event`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1341,"byte_end":1365,"line_start":40,"line_end":40,"column_start":5,"column_end":29,"is_primary":true,"text":[{"text":"use quick_xml::events::Event;","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1337,"byte_end":1368,"line_start":40,"line_end":41,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use quick_xml::events::Event;","highlight_start":1,"highlight_end":30},{"text":"use quick_xml::Reader;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `quick_xml::events::Event`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:40:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse quick_xml::events::Event;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `quick_xml::Reader`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":1372,"byte_end":1389,"line_start":41,"line_end":41,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"use quick_xml::Reader;","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":1368,"byte_end":1392,"line_start":41,"line_end":42,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use quick_xml::Reader;","highlight_start":1,"highlight_end":23},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `quick_xml::Reader`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:41:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse quick_xml::Reader;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `terminal`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":7659,"byte_end":7667,"line_start":199,"line_end":199,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let terminal = match Query::find_terminal_by_user_id(&txn, user_record.id).await {","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_web.rs","byte_start":7659,"byte_end":7667,"line_start":199,"line_end":199,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let terminal = match Query::find_terminal_by_user_id(&txn, user_record.id).await {","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_terminal","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `terminal`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_web.rs:199:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let terminal = match Query::find_terminal_by_user_id(&txn, user_record.id).await {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_terminal`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `terminal_user_registry_records`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":127645,"byte_end":127675,"line_start":3030,"line_end":3030,"column_start":9,"column_end":39,"is_primary":true,"text":[{"text":"    let terminal_user_registry_records = match Query::find_terminal_by_ips(&txn, online_ips.clone()).await {","highlight_start":9,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":127645,"byte_end":127675,"line_start":3030,"line_end":3030,"column_start":9,"column_end":39,"is_primary":true,"text":[{"text":"    let terminal_user_registry_records = match Query::find_terminal_by_ips(&txn, online_ips.clone()).await {","highlight_start":9,"highlight_end":39}],"label":null,"suggested_replacement":"_terminal_user_registry_records","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `terminal_user_registry_records`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:3030:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3030\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let terminal_user_registry_records = match Query::find_terminal_by_ips(&txn, online\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_terminal_user_registry_records`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `terminal_user_not_online_registry_records`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":127993,"byte_end":128034,"line_start":3038,"line_end":3038,"column_start":9,"column_end":50,"is_primary":true,"text":[{"text":"    let terminal_user_not_online_registry_records = match Query::find_terminal_not_online_by_ips(&txn, online_ips.clone()).await {","highlight_start":9,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":127993,"byte_end":128034,"line_start":3038,"line_end":3038,"column_start":9,"column_end":50,"is_primary":true,"text":[{"text":"    let terminal_user_not_online_registry_records = match Query::find_terminal_not_online_by_ips(&txn, online_ips.clone()).await {","highlight_start":9,"highlight_end":50}],"label":null,"suggested_replacement":"_terminal_user_not_online_registry_records","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `terminal_user_not_online_registry_records`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:3038:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3038\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let terminal_user_not_online_registry_records = match Query::find_terminal_not_onli\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_terminal_user_not_online_registry_records`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `train_plan_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":149107,"byte_end":149120,"line_start":3525,"line_end":3525,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let train_plan_id:i32 = match current_train_plan_id.parse::<i32>() {","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":149107,"byte_end":149120,"line_start":3525,"line_end":3525,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let train_plan_id:i32 = match current_train_plan_id.parse::<i32>() {","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_train_plan_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `train_plan_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:3525:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3525\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let train_plan_id:i32 = match current_train_plan_id.parse::<i32>() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_train_plan_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `train_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":150574,"byte_end":150582,"line_start":3566,"line_end":3566,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let train_id = match old_terminal.train_id {","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":150574,"byte_end":150582,"line_start":3566,"line_end":3566,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let train_id = match old_terminal.train_id {","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_train_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `train_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:3566:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3566\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let train_id = match old_terminal.train_id {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_train_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `key` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":200223,"byte_end":200226,"line_start":4837,"line_end":4837,"column_start":13,"column_end":16,"is_primary":true,"text":[{"text":"    let mut key = String::new();","highlight_start":13,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `key` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:4837:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4837\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut key = String::new();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `course_type` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":201482,"byte_end":201493,"line_start":4863,"line_end":4863,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"    let mut course_type = String::new();","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_course_type` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `course_type` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:4863:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4863\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_type = String::new();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_course_type` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_type` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":202295,"byte_end":202306,"line_start":4876,"line_end":4876,"column_start":21,"column_end":32,"is_primary":true,"text":[{"text":"                    course_type = section_value[\"sectionType\"].as_str().unwrap().to_string();","highlight_start":21,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_type` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:4876:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4876\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   course_type = section_value[\"sectionType\"].as_str().unwrap().to_string();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `os_version`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":20218,"byte_end":20228,"line_start":511,"line_end":511,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":20218,"byte_end":20228,"line_start":511,"line_end":511,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"_os_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `os_version`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:511:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m511\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let os_version = match get_system_version(){\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_os_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":26858,"byte_end":26865,"line_start":663,"line_end":663,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    user_id: web::Path<String>,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":26858,"byte_end":26865,"line_start":663,"line_end":663,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    user_id: web::Path<String>,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `user_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:663:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m663\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    user_id: web::Path<String>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `course_type` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":2075,"byte_end":2086,"line_start":60,"line_end":60,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"    let mut course_type = String::new();","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_course_type` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `course_type` is assigned to, but never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:60:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_type = String::new();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_course_type` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_type` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":2864,"byte_end":2875,"line_start":73,"line_end":73,"column_start":21,"column_end":32,"is_primary":true,"text":[{"text":"                    course_type = section_value[\"sectionType\"].as_str().unwrap().to_string();","highlight_start":21,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_type` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:73:21\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   course_type = section_value[\"sectionType\"].as_str().unwrap().to_string();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `course_type` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":8303,"byte_end":8314,"line_start":239,"line_end":239,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"    let mut course_type:String = \"\".to_string();","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_course_type` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `course_type` is assigned to, but never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:239:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m239\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_type:String = \"\".to_string();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_course_type` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_type` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":9035,"byte_end":9046,"line_start":253,"line_end":253,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        course_type = params[\"course_type\"].to_string();","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_type` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:253:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        course_type = params[\"course_type\"].to_string();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `train_course` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":11095,"byte_end":11107,"line_start":296,"line_end":296,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"    let mut train_course: Value = serde_json::Value::Array(Vec::new());","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `train_course` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:296:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut train_course: Value = serde_json::Value::Array(Vec::new());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_res` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":57753,"byte_end":57763,"line_start":1241,"line_end":1241,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"    let mut record_res: HashMap<String, Value> = HashMap::new();","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_res` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1241:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1241\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut record_res: HashMap<String, Value> = HashMap::new();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `raw_answer` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":60925,"byte_end":60935,"line_start":1301,"line_end":1301,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"                    raw_answer = raw_answer.replace(\"\\\"\", \"\");","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `raw_answer` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1301:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    raw_answer = raw_answer.replace(\"\\\"\", \"\");\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":78859,"byte_end":78868,"line_start":1725,"line_end":1725,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    app_state: web::Data<AppState>","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":78859,"byte_end":78868,"line_start":1725,"line_end":1725,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    app_state: web::Data<AppState>","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":"_app_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_state`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1725:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1725\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_state: web::Data<AppState>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `cell_type` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":87945,"byte_end":87954,"line_start":1944,"line_end":1944,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut cell_type: Option<String> = None;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_cell_type` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `cell_type` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1944:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1944\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut cell_type: Option<String> = None;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_cell_type` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `cell_type` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":88333,"byte_end":88342,"line_start":1952,"line_end":1952,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"            cell_type = match item[\"metadata\"][\"type\" ].as_str() {","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `cell_type` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1952:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1952\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            cell_type = match item[\"metadata\"][\"type\" ].as_str() {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `inner_data`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":91016,"byte_end":91026,"line_start":2013,"line_end":2013,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"        if let Some(inner_data) = json_value.get(uuid.clone()) {","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":91016,"byte_end":91026,"line_start":2013,"line_end":2013,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"        if let Some(inner_data) = json_value.get(uuid.clone()) {","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":"_inner_data","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `inner_data`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2013:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2013\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(inner_data) = json_value.get(uuid.clone()) {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_inner_data`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `oj_result` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":103359,"byte_end":103368,"line_start":2315,"line_end":2315,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut oj_result: Value = Value::Null;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `oj_result` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2315:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2315\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut oj_result: Value = Value::Null;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `os_version`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":103859,"byte_end":103869,"line_start":2326,"line_end":2326,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":103859,"byte_end":103869,"line_start":2326,"line_end":2326,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"_os_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `os_version`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2326:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2326\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let os_version = match get_system_version(){\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_os_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `if_load_code`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":132579,"byte_end":132591,"line_start":2965,"line_end":2965,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let if_load_code = params.if_load_code.clone();","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":132579,"byte_end":132591,"line_start":2965,"line_end":2965,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let if_load_code = params.if_load_code.clone();","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_if_load_code","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `if_load_code`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2965:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2965\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let if_load_code = params.if_load_code.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_if_load_code`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_content` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":135313,"byte_end":135327,"line_start":3026,"line_end":3026,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut course_content: Value = serde_json::Value::Array(Vec::new());","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_content` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3026:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3026\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_content: Value = serde_json::Value::Array(Vec::new());\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `course_id` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":143611,"byte_end":143620,"line_start":3187,"line_end":3187,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_course_id` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `course_id` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3187:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_id ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_course_id` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `save_code` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":143636,"byte_end":143645,"line_start":3188,"line_end":3188,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_save_code` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `save_code` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3188:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_code ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_save_code` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `save_run_result` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":143661,"byte_end":143676,"line_start":3189,"line_end":3189,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_save_run_result` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `save_run_result` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3189:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_run_result ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_save_run_result` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `container_info` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":143692,"byte_end":143706,"line_start":3190,"line_end":3190,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut container_info = None;","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `container_info` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3190:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut container_info = None;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_id` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":143782,"byte_end":143791,"line_start":3193,"line_end":3193,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        course_id = course_info_item.id;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_id` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3193:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3193\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        course_id = course_info_item.id;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `save_code` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":143824,"byte_end":143833,"line_start":3194,"line_end":3194,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        save_code = course_info_item.save_code;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `save_code` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3194:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        save_code = course_info_item.save_code;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `save_run_result` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":143873,"byte_end":143888,"line_start":3195,"line_end":3195,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        save_run_result = course_info_item.save_run_result;","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `save_run_result` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3195:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3195\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        save_run_result = course_info_item.save_run_result;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":143607,"byte_end":143620,"line_start":3187,"line_end":3187,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":143607,"byte_end":143611,"line_start":3187,"line_end":3187,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3187:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_id ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":143632,"byte_end":143645,"line_start":3188,"line_end":3188,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":143632,"byte_end":143636,"line_start":3188,"line_end":3188,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3188:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_code ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":143657,"byte_end":143676,"line_start":3189,"line_end":3189,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":143657,"byte_end":143661,"line_start":3189,"line_end":3189,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3189:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_run_result ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `section_id` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":180069,"byte_end":180079,"line_start":3901,"line_end":3901,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"    let mut section_id = 0;","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_section_id` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `section_id` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3901:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3901\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut section_id = 0;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_section_id` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `status` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":180315,"byte_end":180321,"line_start":3906,"line_end":3906,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"    let mut status = None;","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_status` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `status` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3906:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3906\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut status = None;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_status` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `section_id` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":180973,"byte_end":180983,"line_start":3917,"line_end":3917,"column_start":21,"column_end":31,"is_primary":true,"text":[{"text":"                    section_id = section_value[\"sectionID\"].as_i64().unwrap() as i32;","highlight_start":21,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `section_id` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3917:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3917\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    section_id = section_value[\"sectionID\"].as_i64().unwrap() as i32;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":181235,"byte_end":181241,"line_start":3920,"line_end":3920,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"                    status = section_value[\"status\"].as_bool();","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:3920:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3920\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    status = section_value[\"status\"].as_bool();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":199668,"byte_end":199677,"line_start":4383,"line_end":4383,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    app_state: web::Data<AppState>,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":199668,"byte_end":199677,"line_start":4383,"line_end":4383,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    app_state: web::Data<AppState>,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":"_app_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_state`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4383:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4383\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app_state: web::Data<AppState>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `course_slug`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":199705,"byte_end":199716,"line_start":4384,"line_end":4384,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    course_slug: web::Path<String>","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":199705,"byte_end":199716,"line_start":4384,"line_end":4384,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    course_slug: web::Path<String>","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":"_course_slug","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `course_slug`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4384:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4384\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    course_slug: web::Path<String>\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_course_slug`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":200166,"byte_end":200173,"line_start":4395,"line_end":4395,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"    let user_id = user_id.unwrap();","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":200166,"byte_end":200173,"line_start":4395,"line_end":4395,"column_start":9,"column_end":16,"is_primary":true,"text":[{"text":"    let user_id = user_id.unwrap();","highlight_start":9,"highlight_end":16}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `user_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4395:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4395\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let user_id = user_id.unwrap();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `path_dir`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":200203,"byte_end":200211,"line_start":4396,"line_end":4396,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let path_dir = get_path_in_exe_dir(\"course\");","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":200203,"byte_end":200211,"line_start":4396,"line_end":4396,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"    let path_dir = get_path_in_exe_dir(\"course\");","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":"_path_dir","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `path_dir`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4396:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4396\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let path_dir = get_path_in_exe_dir(\"course\");\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_path_dir`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":204800,"byte_end":204814,"line_start":4513,"line_end":4513,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":204800,"byte_end":204804,"line_start":4513,"line_end":4513,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4513:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4513\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut udp_server = app_state.udp_server.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `udp_server`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":208996,"byte_end":209006,"line_start":4600,"line_end":4600,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":208996,"byte_end":209006,"line_start":4600,"line_end":4600,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_udp_server","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `udp_server`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4600:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4600\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut udp_server = app_state.udp_server.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_udp_server`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":208992,"byte_end":209006,"line_start":4600,"line_end":4600,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":208992,"byte_end":208996,"line_start":4600,"line_end":4600,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut udp_server = app_state.udp_server.clone();","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4600:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4600\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut udp_server = app_state.udp_server.clone();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unreachable expression","code":{"code":"unreachable_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":219715,"byte_end":219729,"line_start":4865,"line_end":4865,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"            \"\".to_string()","highlight_start":13,"highlight_end":27}],"label":"unreachable expression","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\course.rs","byte_start":219623,"byte_end":219700,"line_start":4864,"line_end":4864,"column_start":13,"column_end":80,"is_primary":false,"text":[{"text":"            return Err(BusinessError::ProcessError { reason: format!(\"未开始上课\")});","highlight_start":13,"highlight_end":80}],"label":"any code following this expression is unreachable","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unreachable_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unreachable expression\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4865:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4864\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            return Err(BusinessError::ProcessError { reason: format!(\"未开始上课\")});\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14many code following this expression is unreachable\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4865\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"\".to_string()\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11munreachable expression\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unreachable_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `os_version`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":222095,"byte_end":222105,"line_start":4926,"line_end":4926,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":222095,"byte_end":222105,"line_start":4926,"line_end":4926,"column_start":9,"column_end":19,"is_primary":true,"text":[{"text":"    let os_version = match get_system_version(){","highlight_start":9,"highlight_end":19}],"label":null,"suggested_replacement":"_os_version","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `os_version`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4926:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4926\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let os_version = match get_system_version(){\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_os_version`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":222410,"byte_end":222425,"line_start":4931,"line_end":4931,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    let mut python_path = get_path_in_exe_dir(\"../ipython\").join(\"python-3.8.20-embed-amd64\");","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":222410,"byte_end":222414,"line_start":4931,"line_end":4931,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut python_path = get_path_in_exe_dir(\"../ipython\").join(\"python-3.8.20-embed-amd64\");","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:4931:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4931\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut python_path = get_path_in_exe_dir(\"../ipython\").join(\"python-3.8.20-embed-a\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `is_admin` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":227951,"byte_end":227959,"line_start":5075,"line_end":5075,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"    let mut is_admin = true;","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_is_admin` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `is_admin` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5075:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5075\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut is_admin = true;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_is_admin` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `if_load_code`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":228214,"byte_end":228226,"line_start":5083,"line_end":5083,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let if_load_code = true;","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":228214,"byte_end":228226,"line_start":5083,"line_end":5083,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"    let if_load_code = true;","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_if_load_code","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `if_load_code`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5083:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5083\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let if_load_code = true;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_if_load_code`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `submit_record`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":228488,"byte_end":228501,"line_start":5091,"line_end":5091,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let submit_record = json!({});","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":228488,"byte_end":228501,"line_start":5091,"line_end":5091,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let submit_record = json!({});","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_submit_record","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `submit_record`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5091:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5091\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let submit_record = json!({});\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_submit_record`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `status` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":228957,"byte_end":228963,"line_start":5102,"line_end":5102,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"    let mut status = None;","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_status` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `status` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5102:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5102\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut status = None;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_status` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `status` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":229877,"byte_end":229883,"line_start":5116,"line_end":5116,"column_start":21,"column_end":27,"is_primary":true,"text":[{"text":"                    status = section_value[\"status\"].as_bool();","highlight_start":21,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `status` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5116:21\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    status = section_value[\"status\"].as_bool();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `stat`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":230523,"byte_end":230527,"line_start":5130,"line_end":5130,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let stat = json!({","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":230523,"byte_end":230527,"line_start":5130,"line_end":5130,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let stat = json!({","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_stat","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `stat`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5130:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let stat = json!({\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_stat`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_content` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":230926,"byte_end":230940,"line_start":5144,"line_end":5144,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut course_content: Value = serde_json::Value::Array(Vec::new());","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_content` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5144:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5144\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_content: Value = serde_json::Value::Array(Vec::new());\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `is_admin` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":235731,"byte_end":235739,"line_start":5240,"line_end":5240,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"        is_admin = false;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `is_admin` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5240:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5240\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        is_admin = false;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `course_id` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236115,"byte_end":236124,"line_start":5250,"line_end":5250,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_course_id` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `course_id` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5250:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5250\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_id ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_course_id` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `save_code` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236140,"byte_end":236149,"line_start":5251,"line_end":5251,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_save_code` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `save_code` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5251:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5251\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_code ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_save_code` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `save_run_result` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236165,"byte_end":236180,"line_start":5252,"line_end":5252,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_save_run_result` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `save_run_result` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5252:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5252\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_run_result ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_save_run_result` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `container_info` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236196,"byte_end":236210,"line_start":5253,"line_end":5253,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut container_info = None;","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_container_info` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `container_info` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5253:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut container_info = None;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_container_info` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `course_id` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236286,"byte_end":236295,"line_start":5256,"line_end":5256,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        course_id = course_info_item.id;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `course_id` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5256:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        course_id = course_info_item.id;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `save_code` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236328,"byte_end":236337,"line_start":5257,"line_end":5257,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"        save_code = course_info_item.save_code;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `save_code` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5257:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        save_code = course_info_item.save_code;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `save_run_result` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236377,"byte_end":236392,"line_start":5258,"line_end":5258,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        save_run_result = course_info_item.save_run_result;","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `save_run_result` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5258:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5258\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        save_run_result = course_info_item.save_run_result;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `container_info` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236438,"byte_end":236452,"line_start":5259,"line_end":5259,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"        container_info = course_info_item.container_info;","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `container_info` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5259:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        container_info = course_info_item.container_info;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `teams`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236671,"byte_end":236676,"line_start":5266,"line_end":5266,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    let teams = match Query::all_team_find(&txn).await  {","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":236671,"byte_end":236676,"line_start":5266,"line_end":5266,"column_start":9,"column_end":14,"is_primary":true,"text":[{"text":"    let teams = match Query::all_team_find(&txn).await  {","highlight_start":9,"highlight_end":14}],"label":null,"suggested_replacement":"_teams","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `teams`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5266:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let teams = match Query::all_team_find(&txn).await  {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_teams`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `record_time` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":237447,"byte_end":237458,"line_start":5286,"line_end":5286,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"    let mut record_time = Value::Null;","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_record_time` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `record_time` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5286:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5286\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut record_time = Value::Null;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_record_time` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_time` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":237773,"byte_end":237784,"line_start":5295,"line_end":5295,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        record_time = record_times_json;","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_time` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5295:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5295\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        record_time = record_times_json;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable `record_message` is assigned to, but never used","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":237992,"byte_end":238006,"line_start":5303,"line_end":5303,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    let mut record_message = json!({});","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider using `_record_message` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable `record_message` is assigned to, but never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5303:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut record_message = json!({});\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `_record_message` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_message` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":244033,"byte_end":244047,"line_start":5411,"line_end":5411,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"                record_message = serde_json::to_value(record_res).unwrap();","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_message` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5411:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5411\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                record_message = serde_json::to_value(record_res).unwrap();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_message` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":244475,"byte_end":244489,"line_start":5419,"line_end":5419,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"                record_message = serde_json::to_value(record_map).unwrap();","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_message` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5419:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5419\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                record_message = serde_json::to_value(record_map).unwrap();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_message` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":244673,"byte_end":244687,"line_start":5424,"line_end":5424,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"                record_message = record;","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_message` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5424:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5424\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                record_message = record;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `record_message` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":246211,"byte_end":246225,"line_start":5452,"line_end":5452,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"                record_message = code_result;","highlight_start":17,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `record_message` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5452:17\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5452\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                record_message = code_result;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236111,"byte_end":236124,"line_start":5250,"line_end":5250,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":236111,"byte_end":236115,"line_start":5250,"line_end":5250,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut course_id ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5250:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5250\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut course_id ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236136,"byte_end":236149,"line_start":5251,"line_end":5251,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":236136,"byte_end":236140,"line_start":5251,"line_end":5251,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut save_code ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5251:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5251\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_code ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":236161,"byte_end":236180,"line_start":5252,"line_end":5252,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":236161,"byte_end":236165,"line_start":5252,"line_end":5252,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut save_run_result ;","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5252:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5252\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut save_run_result ;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":239619,"byte_end":239630,"line_start":5335,"line_end":5335,"column_start":29,"column_end":40,"is_primary":true,"text":[{"text":"                        let mut message;","highlight_start":29,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":239619,"byte_end":239623,"line_start":5335,"line_end":5335,"column_start":29,"column_end":33,"is_primary":true,"text":[{"text":"                        let mut message;","highlight_start":29,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:5335:29\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5335\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        let mut message;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `sender`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31770,"byte_end":31776,"line_start":795,"line_end":795,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    sender: mpsc::UnboundedSender<FileTransmitPacket>    ","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31770,"byte_end":31776,"line_start":795,"line_end":795,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    sender: mpsc::UnboundedSender<FileTransmitPacket>    ","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"_sender","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `sender`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:795:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m795\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sender: mpsc::UnboundedSender<FileTransmitPacket>    \u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_sender`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `udp_file_server`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31842,"byte_end":31857,"line_start":797,"line_end":797,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"    let mut udp_file_server = app_state.udp_file_server.clone();","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31842,"byte_end":31857,"line_start":797,"line_end":797,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"    let mut udp_file_server = app_state.udp_file_server.clone();","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":"_udp_file_server","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `udp_file_server`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:797:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m797\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut udp_file_server = app_state.udp_file_server.clone();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_udp_file_server`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31838,"byte_end":31857,"line_start":797,"line_end":797,"column_start":9,"column_end":28,"is_primary":true,"text":[{"text":"    let mut udp_file_server = app_state.udp_file_server.clone();","highlight_start":9,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":31838,"byte_end":31842,"line_start":797,"line_end":797,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"    let mut udp_file_server = app_state.udp_file_server.clone();","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variable does not need to be mutable\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:797:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m797\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut udp_file_server = app_state.udp_file_server.clone();\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: remove this `mut`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Digest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":2258,"byte_end":2264,"line_start":54,"line_end":54,"column_start":20,"column_end":26,"is_primary":true,"text":[{"text":"use sha2::{Sha256, Digest}; // 用于生成 sha2","highlight_start":20,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Digest`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:54:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse sha2::{Sha256, Digest}; // 用于生成 sha2\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::io::Write`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":47,"byte_end":61,"line_start":3,"line_end":3,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::io::Write;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::io::Write`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::io::Write;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `msg_clone`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":19178,"byte_end":19187,"line_start":541,"line_end":541,"column_start":29,"column_end":38,"is_primary":true,"text":[{"text":"                        let msg_clone = Arc::new(msg.msg.clone());","highlight_start":29,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"api\\src\\lib.rs","byte_start":19178,"byte_end":19187,"line_start":541,"line_end":541,"column_start":29,"column_end":38,"is_primary":true,"text":[{"text":"                        let msg_clone = Arc::new(msg.msg.clone());","highlight_start":29,"highlight_end":38}],"label":null,"suggested_replacement":"_msg_clone","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `msg_clone`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:541:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m541\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        let msg_clone = Arc::new(msg.msg.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_msg_clone`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `on_client_message` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\lib.rs","byte_start":14456,"byte_end":14473,"line_start":431,"line_end":431,"column_start":4,"column_end":21,"is_primary":true,"text":[{"text":"fn on_client_message(msg:&ClientMessage){","highlight_start":4,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `on_client_message` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\lib.rs:431:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m431\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn on_client_message(msg:&ClientMessage){\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `binary` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\controller_admin.rs","byte_start":195785,"byte_end":195794,"line_start":4709,"line_end":4709,"column_start":8,"column_end":17,"is_primary":false,"text":[{"text":"struct MicroType {","highlight_start":8,"highlight_end":17}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\controller_admin.rs","byte_start":195849,"byte_end":195855,"line_start":4712,"line_end":4712,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    binary: bool,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`MicroType` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `binary` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\controller_admin.rs:4712:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4709\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct MicroType {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4712\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    binary: bool,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `MicroType` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `run_jupyter_lab`, `check_ipython`, `ipython_version`, and `kill_ipython` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\file_operate.rs","byte_start":1334,"byte_end":1347,"line_start":39,"line_end":39,"column_start":8,"column_end":21,"is_primary":false,"text":[{"text":"struct IpythonConfig {","highlight_start":8,"highlight_end":21}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":1843,"byte_end":1858,"line_start":58,"line_end":58,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    run_jupyter_lab:String,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":1900,"byte_end":1913,"line_start":60,"line_end":60,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    check_ipython: String,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":1928,"byte_end":1943,"line_start":61,"line_end":61,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    ipython_version:String,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\file_operate.rs","byte_start":1985,"byte_end":1997,"line_start":63,"line_end":63,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    kill_ipython:String","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`IpythonConfig` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `run_jupyter_lab`, `check_ipython`, `ipython_version`, and `kill_ipython` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\file_operate.rs:58:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct IpythonConfig {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m58\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    run_jupyter_lab:String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m59\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    // 检查Ipython环境\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m60\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    check_ipython: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ipython_version:String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m62\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    // 关闭Ipython环境\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    kill_ipython:String\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `IpythonConfig` has a derived impl for the trait `Debug`, but this is intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `Options` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":5534,"byte_end":5541,"line_start":135,"line_end":135,"column_start":8,"column_end":15,"is_primary":true,"text":[{"text":"struct Options {","highlight_start":8,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `Options` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:135:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct Options {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `Answer` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":5648,"byte_end":5654,"line_start":142,"line_end":142,"column_start":8,"column_end":14,"is_primary":true,"text":[{"text":"struct Answer {","highlight_start":8,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `Answer` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:142:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct Answer {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `Question` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":5740,"byte_end":5748,"line_start":148,"line_end":148,"column_start":8,"column_end":16,"is_primary":true,"text":[{"text":"struct Question {","highlight_start":8,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `Question` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:148:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct Question {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `OIData` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":5940,"byte_end":5946,"line_start":158,"line_end":158,"column_start":8,"column_end":14,"is_primary":true,"text":[{"text":"struct OIData {","highlight_start":8,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `OIData` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:158:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m158\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct OIData {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `JudgeResult` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":56071,"byte_end":56082,"line_start":1199,"line_end":1199,"column_start":8,"column_end":19,"is_primary":true,"text":[{"text":"struct JudgeResult{","highlight_start":8,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `JudgeResult` is never constructed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1199:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct JudgeResult{\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `JudgeStep` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":71595,"byte_end":71604,"line_start":1542,"line_end":1542,"column_start":8,"column_end":17,"is_primary":true,"text":[{"text":"struct JudgeStep {","highlight_start":8,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `JudgeStep` is never constructed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1542:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1542\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct JudgeStep {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `Costumes` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":71790,"byte_end":71798,"line_start":1552,"line_end":1552,"column_start":8,"column_end":16,"is_primary":true,"text":[{"text":"struct Costumes {","highlight_start":8,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `Costumes` is never constructed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1552:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1552\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct Costumes {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `Blocks` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":72065,"byte_end":72071,"line_start":1562,"line_end":1562,"column_start":8,"column_end":14,"is_primary":true,"text":[{"text":"struct Blocks {","highlight_start":8,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `Blocks` is never constructed\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:1562:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1562\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct Blocks {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `extract_values` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":128299,"byte_end":128313,"line_start":2856,"line_end":2856,"column_start":4,"column_end":18,"is_primary":true,"text":[{"text":"fn extract_values(input: String) -> Vec<String> {","highlight_start":4,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `extract_values` is never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:2856:4\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2856\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn extract_values(input: String) -> Vec<String> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\context.rs","byte_start":1334,"byte_end":1347,"line_start":50,"line_end":50,"column_start":10,"column_end":23,"is_primary":false,"text":[{"text":"pub enum BusinessError {","highlight_start":10,"highlight_end":23}],"label":"`BusinessError` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1314,"line_start":49,"line_end":49,"column_start":10,"column_end":14,"is_primary":false,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":14}],"label":"`Fail` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1314,"line_start":49,"line_end":49,"column_start":10,"column_end":14,"is_primary":false,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Fail)]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\synstructure-0.12.6\\src\\macros.rs","byte_start":2861,"byte_end":2961,"line_start":94,"line_end":96,"column_start":9,"column_end":41,"is_primary":false,"text":[{"text":"        pub fn $derives(","highlight_start":9,"highlight_end":25},{"text":"            i: $crate::macros::TokenStream","highlight_start":1,"highlight_end":43},{"text":"        ) -> $crate::macros::TokenStream {","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1314,"line_start":49,"line_end":49,"column_start":10,"column_end":14,"is_primary":false,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":14}],"label":"move the `impl` block outside of this constant `_DERIVE_failure_Fail_FOR_BusinessError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1314,"line_start":49,"line_end":49,"column_start":10,"column_end":14,"is_primary":false,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Fail)]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\synstructure-0.12.6\\src\\macros.rs","byte_start":2861,"byte_end":2961,"line_start":94,"line_end":96,"column_start":9,"column_end":41,"is_primary":false,"text":[{"text":"        pub fn $derives(","highlight_start":9,"highlight_end":25},{"text":"            i: $crate::macros::TokenStream","highlight_start":1,"highlight_end":43},{"text":"        ) -> $crate::macros::TokenStream {","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1310,"line_start":49,"line_end":49,"column_start":10,"column_end":10,"is_primary":true,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1314,"line_start":49,"line_end":49,"column_start":10,"column_end":14,"is_primary":false,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Fail)]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\synstructure-0.12.6\\src\\macros.rs","byte_start":2861,"byte_end":2961,"line_start":94,"line_end":96,"column_start":9,"column_end":41,"is_primary":false,"text":[{"text":"        pub fn $derives(","highlight_start":9,"highlight_end":25},{"text":"            i: $crate::macros::TokenStream","highlight_start":1,"highlight_end":43},{"text":"        ) -> $crate::macros::TokenStream {","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the derive macro `Fail` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the derive macro `Fail` may come from an old version of the `failure_derive` crate, try updating your dependency with `cargo update -p failure_derive`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"items in an anonymous const item (`const _: () = { ... }`) are treated as in the same scope as the anonymous const's declaration for the purpose of this lint","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(non_local_definitions)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\context.rs:49:10\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Fail, Debug)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`Fail` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this constant `_DERIVE_failure_Fail_FOR_BusinessError`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum BusinessError {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`BusinessError` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the derive macro `Fail` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the derive macro `Fail` may come from an old version of the `failure_derive` crate, try updating your dependency with `cargo update -p failure_derive`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: items in an anonymous const item (`const _: () = { ... }`) are treated as in the same scope as the anonymous const's declaration for the purpose of this lint\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(non_local_definitions)]` on by default\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the derive macro `Fail` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"non-local `impl` definition, `impl` blocks should be written at the same level as their item","code":{"code":"non_local_definitions","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\context.rs","byte_start":1334,"byte_end":1347,"line_start":50,"line_end":50,"column_start":10,"column_end":23,"is_primary":false,"text":[{"text":"pub enum BusinessError {","highlight_start":10,"highlight_end":23}],"label":"`BusinessError` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1314,"line_start":49,"line_end":49,"column_start":10,"column_end":14,"is_primary":false,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":14}],"label":"`Display` is not local","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1314,"line_start":49,"line_end":49,"column_start":10,"column_end":14,"is_primary":false,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Fail)]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\synstructure-0.12.6\\src\\macros.rs","byte_start":2861,"byte_end":2961,"line_start":94,"line_end":96,"column_start":9,"column_end":41,"is_primary":false,"text":[{"text":"        pub fn $derives(","highlight_start":9,"highlight_end":25},{"text":"            i: $crate::macros::TokenStream","highlight_start":1,"highlight_end":43},{"text":"        ) -> $crate::macros::TokenStream {","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1314,"line_start":49,"line_end":49,"column_start":10,"column_end":14,"is_primary":false,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":14}],"label":"move the `impl` block outside of this constant `_DERIVE_failure_core_fmt_Display_FOR_BusinessError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1314,"line_start":49,"line_end":49,"column_start":10,"column_end":14,"is_primary":false,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Fail)]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\synstructure-0.12.6\\src\\macros.rs","byte_start":2861,"byte_end":2961,"line_start":94,"line_end":96,"column_start":9,"column_end":41,"is_primary":false,"text":[{"text":"        pub fn $derives(","highlight_start":9,"highlight_end":25},{"text":"            i: $crate::macros::TokenStream","highlight_start":1,"highlight_end":43},{"text":"        ) -> $crate::macros::TokenStream {","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1310,"line_start":49,"line_end":49,"column_start":10,"column_end":10,"is_primary":true,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"api\\src\\context.rs","byte_start":1310,"byte_end":1314,"line_start":49,"line_end":49,"column_start":10,"column_end":14,"is_primary":false,"text":[{"text":"#[derive(Fail, Debug)]","highlight_start":10,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Fail)]","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\synstructure-0.12.6\\src\\macros.rs","byte_start":2861,"byte_end":2961,"line_start":94,"line_end":96,"column_start":9,"column_end":41,"is_primary":false,"text":[{"text":"        pub fn $derives(","highlight_start":9,"highlight_end":25},{"text":"            i: $crate::macros::TokenStream","highlight_start":1,"highlight_end":43},{"text":"        ) -> $crate::macros::TokenStream {","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the derive macro `Fail` defines the non-local `impl`, and may need to be changed","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the derive macro `Fail` may come from an old version of the `failure_derive` crate, try updating your dependency with `cargo update -p failure_derive`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"items in an anonymous const item (`const _: () = { ... }`) are treated as in the same scope as the anonymous const's declaration for the purpose of this lint","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: non-local `impl` definition, `impl` blocks should be written at the same level as their item\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\context.rs:49:10\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Fail, Debug)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`Display` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove the `impl` block outside of this constant `_DERIVE_failure_core_fmt_Display_FOR_BusinessError`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m50\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum BusinessError {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`BusinessError` is not local\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the derive macro `Fail` defines the non-local `impl`, and may need to be changed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the derive macro `Fail` may come from an old version of the `failure_derive` crate, try updating your dependency with `cargo update -p failure_derive`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: an `impl` is never scoped, even when it is nested inside an item, as it may impact type checking outside of that item, which can be the case if neither the trait or the self type are at the same nesting level as the `impl`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: items in an anonymous const item (`const _: () = { ... }`) are treated as in the same scope as the anonymous const's declaration for the purpose of this lint\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this warning originates in the derive macro `Fail` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `UUID` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":5776,"byte_end":5780,"line_start":150,"line_end":150,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    UUID: String,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_snake_case)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":5776,"byte_end":5780,"line_start":150,"line_end":150,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    UUID: String,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"uuid","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `UUID` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:150:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    UUID: String,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(non_snake_case)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"structure field `questionType` should have a snake case name","code":{"code":"non_snake_case","explanation":null},"level":"warning","spans":[{"file_name":"api\\src\\course.rs","byte_start":5845,"byte_end":5857,"line_start":153,"line_end":153,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    questionType: String,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to snake case","code":null,"level":"help","spans":[{"file_name":"api\\src\\course.rs","byte_start":5845,"byte_end":5857,"line_start":153,"line_end":153,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    questionType: String,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":"question_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: structure field `questionType` should have a snake case name\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mapi\\src\\course.rs:153:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    questionType: String,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: convert the identifier to snake case: `question_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"143 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 143 warnings emitted\u001b[0m\n\n"}
