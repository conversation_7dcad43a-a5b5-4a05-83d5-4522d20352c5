{"rustc": 7868289081541623310, "features": "[\"default\", \"display\", \"error\", \"from\", \"std\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 7165309211519594838, "profile": 1613925905003419231, "path": 2658484564868831260, "deps": [[14526174249165944584, "derive_more_impl", false, 10455393281520994794]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-f50d0553fb8836c4\\dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}