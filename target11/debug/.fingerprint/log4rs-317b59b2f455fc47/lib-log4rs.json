{"rustc": 7868289081541623310, "features": "[\"all_components\", \"ansi_writer\", \"chrono\", \"compound_policy\", \"config_parsing\", \"console_appender\", \"console_writer\", \"default\", \"delete_roller\", \"file_appender\", \"fixed_window_roller\", \"humantime\", \"json_encoder\", \"libc\", \"log-mdc\", \"onstartup_trigger\", \"parking_lot\", \"pattern_encoder\", \"rand\", \"rolling_file_appender\", \"serde\", \"serde-value\", \"serde_json\", \"serde_yaml\", \"simple_writer\", \"size_trigger\", \"thread-id\", \"threshold_filter\", \"time_trigger\", \"typemap-ors\", \"winapi\", \"yaml_format\"]", "declared_features": "[\"all_components\", \"ansi_writer\", \"background_rotation\", \"chrono\", \"compound_policy\", \"config_parsing\", \"console_appender\", \"console_writer\", \"default\", \"delete_roller\", \"file_appender\", \"fixed_window_roller\", \"flate2\", \"gzip\", \"humantime\", \"json_encoder\", \"json_format\", \"libc\", \"log-mdc\", \"log_kv\", \"onstartup_trigger\", \"parking_lot\", \"pattern_encoder\", \"rand\", \"rolling_file_appender\", \"serde\", \"serde-value\", \"serde_json\", \"serde_yaml\", \"simple_writer\", \"size_trigger\", \"thread-id\", \"threshold_filter\", \"time_trigger\", \"toml\", \"toml_format\", \"typemap-ors\", \"winapi\", \"yaml_format\", \"zstd\"]", "target": 10889050935924218042, "profile": 17066504456555098960, "path": 2557250862492009427, "deps": [[503842845364652431, "chrono", false, 2694722972666749306], [1232198224951696867, "unicode_segmentation", false, 2595049756132880593], [1345404220202658316, "fnv", false, 11315970877785559848], [1852463361802237065, "anyhow", false, 12642207311896370823], [2786110112969004522, "mock_instant", false, 8786131151997573220], [4255213592622242520, "typemap_ors", false, 9770874153742266933], [4336745513838352383, "thiserror", false, 485849119894558803], [5385126873501015737, "serde_value", false, 156944375346285737], [9614479274285663593, "serde_yaml", false, 10391602129817611009], [10020888071089587331, "<PERSON>ap<PERSON>", false, 17234678956380023108], [11293676373856528358, "derive_more", false, 17138922006857402528], [11916940916964035392, "rand", false, 12758954955869096798], [12459942763388630573, "parking_lot", false, 10834859847349329858], [12832915883349295919, "serde_json", false, 10973443172856390042], [13066042571740262168, "log", false, 7798813343169351211], [13548984313718623784, "serde", false, 6688476347181372788], [14076699970472871123, "humantime", false, 15715570342176909443], [14217372430137802392, "thread_id", false, 4793501166824569698], [14306042979243042769, "arc_swap", false, 11400888865078247246], [16037874089270104281, "log_mdc", false, 16891985747052986173]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\log4rs-317b59b2f455fc47\\dep-lib-log4rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}