{"rustc": 7868289081541623310, "features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"attributes\", \"crossbeam-utils\", \"default\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"tokio1\", \"wasm-bindgen-futures\"]", "declared_features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"async-process\", \"attributes\", \"crossbeam-utils\", \"default\", \"docs\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"io_safety\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"surf\", \"tokio02\", \"tokio03\", \"tokio1\", \"unstable\", \"wasm-bindgen-futures\"]", "target": 9139776409365598091, "profile": 2241668132362809309, "path": 3132908549547361335, "deps": [[5103565458935487, "futures_io", false, 16581260027880997218], [198136567835728122, "memchr", false, 2193736769135005334], [1615478164327904835, "pin_utils", false, 13723329715074829922], [1906322745568073236, "pin_project_lite", false, 15152602122021925890], [3722963349756955755, "once_cell", false, 17804166380184082805], [4468123440088164316, "crossbeam_utils", false, 6109711896492874890], [5302544599749092241, "async_channel", false, 4612152493620370325], [7620660491849607393, "futures_core", false, 16247667574460830795], [9090520973410485560, "futures_lite", false, 12926514480562315200], [9511937138168509053, "async_attributes", false, 17813721758134007933], [13066042571740262168, "log", false, 7798813343169351211], [13330646740533913557, "async_global_executor", false, 11718381663404463558], [14660869117855173827, "async_lock", false, 4018761011171482446], [14767213526276824509, "slab", false, 3081161731653374015], [15550619062825872913, "async_io", false, 11879141352308647981], [17569958903244628888, "kv_log_macro", false, 5293871907532847558]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-std-5c60ac00e2094774\\dep-lib-async_std", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}