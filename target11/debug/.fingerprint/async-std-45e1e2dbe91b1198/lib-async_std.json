{"rustc": 7868289081541623310, "features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"attributes\", \"crossbeam-utils\", \"default\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"tokio1\", \"wasm-bindgen-futures\"]", "declared_features": "[\"alloc\", \"async-attributes\", \"async-channel\", \"async-global-executor\", \"async-io\", \"async-lock\", \"async-process\", \"attributes\", \"crossbeam-utils\", \"default\", \"docs\", \"futures-channel\", \"futures-core\", \"futures-io\", \"futures-lite\", \"gloo-timers\", \"io_safety\", \"kv-log-macro\", \"log\", \"memchr\", \"once_cell\", \"pin-project-lite\", \"pin-utils\", \"slab\", \"std\", \"surf\", \"tokio02\", \"tokio03\", \"tokio1\", \"unstable\", \"wasm-bindgen-futures\"]", "target": 9139776409365598091, "profile": 15657897354478470176, "path": 3132908549547361335, "deps": [[5103565458935487, "futures_io", false, 8524945502723996541], [198136567835728122, "memchr", false, 9086700952074288555], [1615478164327904835, "pin_utils", false, 2105454296310835692], [1906322745568073236, "pin_project_lite", false, 14377486027600573708], [3722963349756955755, "once_cell", false, 14227545244991549055], [4468123440088164316, "crossbeam_utils", false, 12088958592396463018], [5302544599749092241, "async_channel", false, 8749953432410372313], [7620660491849607393, "futures_core", false, 10185287431925956518], [9090520973410485560, "futures_lite", false, 8330740482625529843], [9511937138168509053, "async_attributes", false, 15094625360223419622], [13066042571740262168, "log", false, 346612371326456591], [13330646740533913557, "async_global_executor", false, 5049586332395260847], [14660869117855173827, "async_lock", false, 9673945428760558798], [14767213526276824509, "slab", false, 8492086034571287643], [15550619062825872913, "async_io", false, 423086255041800308], [17569958903244628888, "kv_log_macro", false, 361314533037634250]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-std-45e1e2dbe91b1198\\dep-lib-async_std", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}