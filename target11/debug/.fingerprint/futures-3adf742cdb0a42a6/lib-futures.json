{"rustc": 7868289081541623310, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 3916138203474788610, "deps": [[5103565458935487, "futures_io", false, 8524945502723996541], [1811549171721445101, "futures_channel", false, 8897691913039395542], [7013762810557009322, "futures_sink", false, 9365711432218614735], [7620660491849607393, "futures_core", false, 10185287431925956518], [10629569228670356391, "futures_util", false, 15851095376711556926], [12779779637805422465, "futures_executor", false, 5503455852402611022], [16240732885093539806, "futures_task", false, 9426041057328841975]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-3adf742cdb0a42a6\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}