{"rustc": 7868289081541623310, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"pkg-config\", \"unlock_notify\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"winsqlite3\", \"with-asan\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 773816996529785618, "deps": [[3214373357989284387, "pkg_config", false, 8620763788454315645], [4150023412585039077, "cc", false, 16397232532277495113], [12933202132622624734, "vcpkg", false, 12311377066008835980]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\libsqlite3-sys-49438f514bf43f1a\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}