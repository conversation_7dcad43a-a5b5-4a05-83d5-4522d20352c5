{"rustc": 7868289081541623310, "features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"not\", \"rustc_version\", \"sum\", \"try_into\", \"unwrap\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 12153973509411789784, "profile": 2225463790103693989, "path": 3953000956598752294, "deps": [[11082282709338087849, "quote", false, 14907447346729856509], [11688815897905910532, "syn", false, 7015498159373711073], [14285738760999836560, "proc_macro2", false, 9754575219844062882], [14907448031486326382, "convert_case", false, 3711081923970068374]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-5e520a5e2b824363\\dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}