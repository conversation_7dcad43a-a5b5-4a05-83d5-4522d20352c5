{"rustc": 7868289081541623310, "features": "[\"color\", \"default\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 15599109589607159429, "path": 15570601023627652985, "deps": [[568339519768649955, "clap_derive", false, 7588746528084106252], [7170476064230165770, "clap_builder", false, 10638549481335741586]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-c1891ef64d167003\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}